# Installation Guide

This guide provides step-by-step instructions for installing and setting up the Cybersecurity Scanner on different platforms.

## System Requirements

### Minimum Requirements
- **Operating System**: Windows 10/11, Linux (Ubuntu 18.04+), macOS 10.14+
- **Python**: 3.7 or higher
- **RAM**: 2GB minimum, 4GB recommended
- **Disk Space**: 500MB for installation, additional space for scan results
- **Network**: Internet connection for external scans and dependency downloads

### Recommended Requirements
- **Python**: 3.9 or higher
- **RAM**: 8GB for large-scale scanning
- **CPU**: Multi-core processor for parallel scanning
- **Network**: High-speed internet for faster scans

## Windows Installation

### Method 1: Automated Setup (Recommended)

1. **Download the Project**
   ```cmd
   git clone <repository-url>
   cd cybersecurity-scanner
   ```

2. **Run Automated Setup**
   ```cmd
   python setup_windows.py
   ```
   
   This script will:
   - Install Python dependencies
   - Download required libraries
   - Set up the environment
   - Verify installation

3. **Verify Installation**
   ```cmd
   python cybersecurity_orchestrator.py --help
   ```

### Method 2: Manual Installation

1. **Install Python Dependencies**
   ```cmd
   pip install -r requirements.txt
   ```

2. **Install Optional Tools**
   - Download and install [Nmap](https://nmap.org/download.html)
   - Add Nmap to your system PATH
   - Install OpenSSL (usually included with Git for Windows)

3. **Verify Dependencies**
   ```cmd
   python -c "import requests, dns.resolver, cryptography; print('Dependencies OK')"
   ```

### Method 3: Using Virtual Environment

1. **Create Virtual Environment**
   ```cmd
   python -m venv cybersec_env
   cybersec_env\Scripts\activate
   ```

2. **Install Dependencies**
   ```cmd
   pip install -r requirements.txt
   ```

3. **Run Setup**
   ```cmd
   python setup_windows.py
   ```

## Linux Installation

### Ubuntu/Debian

1. **Install System Dependencies**
   ```bash
   sudo apt update
   sudo apt install python3 python3-pip python3-venv git nmap openssl
   ```

2. **Clone Repository**
   ```bash
   git clone <repository-url>
   cd cybersecurity-scanner
   ```

3. **Create Virtual Environment**
   ```bash
   python3 -m venv venv
   source venv/bin/activate
   ```

4. **Install Python Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

5. **Verify Installation**
   ```bash
   python cybersecurity_orchestrator.py --help
   ```

### CentOS/RHEL/Fedora

1. **Install System Dependencies**
   ```bash
   # CentOS/RHEL
   sudo yum install python3 python3-pip git nmap openssl
   
   # Fedora
   sudo dnf install python3 python3-pip git nmap openssl
   ```

2. **Follow steps 2-5 from Ubuntu installation**

## macOS Installation

### Using Homebrew (Recommended)

1. **Install Homebrew** (if not already installed)
   ```bash
   /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
   ```

2. **Install Dependencies**
   ```bash
   brew install python3 git nmap openssl
   ```

3. **Clone and Setup**
   ```bash
   git clone <repository-url>
   cd cybersecurity-scanner
   python3 -m venv venv
   source venv/bin/activate
   pip install -r requirements.txt
   ```

### Using MacPorts

1. **Install Dependencies**
   ```bash
   sudo port install python39 py39-pip git nmap openssl
   ```

2. **Follow setup steps from Homebrew method**

## Docker Installation

### Using Docker Compose (Recommended)

1. **Create docker-compose.yml**
   ```yaml
   version: '3.8'
   services:
     cybersec-scanner:
       build: .
       ports:
         - "8080:8080"
       volumes:
         - ./results:/app/results
       environment:
         - PYTHONUNBUFFERED=1
   ```

2. **Build and Run**
   ```bash
   docker-compose up --build
   ```

### Using Dockerfile

1. **Create Dockerfile**
   ```dockerfile
   FROM python:3.9-slim
   
   RUN apt-get update && apt-get install -y \
       nmap \
       openssl \
       git \
       && rm -rf /var/lib/apt/lists/*
   
   WORKDIR /app
   COPY requirements.txt .
   RUN pip install -r requirements.txt
   
   COPY . .
   EXPOSE 8080
   
   CMD ["python", "web_server.py"]
   ```

2. **Build and Run**
   ```bash
   docker build -t cybersec-scanner .
   docker run -p 8080:8080 cybersec-scanner
   ```

## Dependency Details

### Required Python Packages

| Package | Version | Purpose |
|---------|---------|---------|
| requests | ≥2.25.0 | HTTP client for web requests |
| dnspython | ≥2.1.0 | DNS resolution and analysis |
| cryptography | ≥3.4.0 | SSL/TLS certificate handling |
| beautifulsoup4 | ≥4.9.0 | HTML parsing |
| python-whois | ≥0.7.0 | Domain information lookup |

### Optional System Tools

| Tool | Purpose | Fallback |
|------|---------|----------|
| nmap | Advanced port scanning | Python socket scanning |
| openssl | SSL/TLS analysis | Python cryptography library |
| dig | DNS queries | Python dnspython |
| curl | HTTP testing | Python requests |

## Configuration

### Environment Variables

Create a `.env` file in the project root:
```env
# Server Configuration
SERVER_HOST=localhost
SERVER_PORT=8080

# Scanning Configuration
DEFAULT_TIMEOUT=30
MAX_CONCURRENT_SCANS=5
RESULTS_DIRECTORY=results

# External Tools
NMAP_PATH=/usr/bin/nmap
OPENSSL_PATH=/usr/bin/openssl

# Logging
LOG_LEVEL=INFO
LOG_FILE=scanner.log
```

### Configuration File

Create `config.json`:
```json
{
  "scanning": {
    "default_timeout": 30,
    "port_scan_timeout": 60,
    "ssl_timeout": 15,
    "dns_timeout": 10
  },
  "server": {
    "host": "localhost",
    "port": 8080,
    "debug": false
  },
  "paths": {
    "results_dir": "results",
    "logs_dir": "logs",
    "temp_dir": "temp"
  }
}
```

## Verification

### Test Installation

1. **Basic Functionality Test**
   ```bash
   python cybersecurity_orchestrator.py google.com --scan-type quick
   ```

2. **Web Interface Test**
   ```bash
   python web_server.py &
   curl http://localhost:8080/api/scan -X POST -H "Content-Type: application/json" -d '{"target":"google.com","scan_type":"quick"}'
   ```

3. **Module Import Test**
   ```python
   python -c "
   from python_modules import port_scanner, ssl_checker, http_security
   print('All modules imported successfully')
   "
   ```

## Troubleshooting Installation

### Common Issues

**Python Version Conflicts**
```bash
# Check Python version
python --version
python3 --version

# Use specific Python version
python3.9 -m pip install -r requirements.txt
```

**Permission Errors (Linux/macOS)**
```bash
# Use virtual environment
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

**Network/Firewall Issues**
```bash
# Test network connectivity
ping google.com
nslookup google.com

# Check firewall settings
sudo ufw status  # Ubuntu
sudo firewall-cmd --list-all  # CentOS/RHEL
```

**Missing System Dependencies**
```bash
# Ubuntu/Debian
sudo apt install build-essential python3-dev

# CentOS/RHEL
sudo yum groupinstall "Development Tools"
sudo yum install python3-devel
```

### Performance Optimization

1. **Increase System Limits**
   ```bash
   # Increase file descriptor limits
   ulimit -n 65536
   
   # Add to /etc/security/limits.conf
   * soft nofile 65536
   * hard nofile 65536
   ```

2. **Python Optimization**
   ```bash
   # Use faster JSON library
   pip install ujson
   
   # Use faster DNS resolver
   pip install aiodns
   ```

3. **System Tuning**
   ```bash
   # Increase network buffers
   echo 'net.core.rmem_max = 16777216' >> /etc/sysctl.conf
   echo 'net.core.wmem_max = 16777216' >> /etc/sysctl.conf
   sysctl -p
   ```

## Uninstallation

### Remove Virtual Environment
```bash
# Deactivate if active
deactivate

# Remove directory
rm -rf venv  # Linux/macOS
rmdir /s venv  # Windows
```

### Remove System Packages
```bash
# Ubuntu/Debian
sudo apt remove nmap openssl

# CentOS/RHEL
sudo yum remove nmap openssl

# macOS
brew uninstall nmap openssl
```

### Clean Up Files
```bash
# Remove project directory
rm -rf cybersecurity-scanner

# Remove configuration files
rm ~/.cybersec_scanner_config
```
