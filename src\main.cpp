#include <iostream>
#include <string>
#include <vector>
#include <thread>
#include <chrono>
#include <fstream>
#include "dns_health_checker.h"

// Simple command line parsing for cross-platform compatibility
struct CommandLineArgs
{
    std::string domain;
    std::string output_file = "dns_health_report.json";
    bool check_local = false;
    int schedule_minutes = 0;
    bool show_help = false;
    bool json_output = true;
};

CommandLineArgs parseArguments(int argc, char *argv[])
{
    CommandLineArgs args;

    for (int i = 1; i < argc; i++)
    {
        std::string arg = argv[i];

        if (arg == "-h" || arg == "--help")
        {
            args.show_help = true;
        }
        else if (arg == "-l" || arg == "--local")
        {
            args.check_local = true;
        }
        else if ((arg == "-d" || arg == "--domain") && i + 1 < argc)
        {
            args.domain = argv[++i];
        }
        else if ((arg == "-o" || arg == "--output") && i + 1 < argc)
        {
            args.output_file = argv[++i];
        }
        else if ((arg == "-s" || arg == "--schedule") && i + 1 < argc)
        {
            args.schedule_minutes = std::stoi(argv[++i]);
        }
    }

    return args;
}

void printUsage(const char *program_name)
{
    std::cout << "DNS Health Checker - A tool to check DNS configuration health\n\n";
    std::cout << "Usage: " << program_name << " [options]\n\n";
    std::cout << "Options:\n";
    std::cout << "  -h, --help                 Show this help message\n";
    std::cout << "  -d, --domain DOMAIN        Specify domain to check\n";
    std::cout << "  -l, --local                Check local machine DNS settings\n";
    std::cout << "  -o, --output FILE          Specify output JSON file (default: dns_health_report.json)\n";
    std::cout << "  -s, --schedule MINUTES     Schedule recurring checks (in minutes)\n\n";
    std::cout << "Examples:\n";
    std::cout << "  " << program_name << " -d example.com\n";
    std::cout << "  " << program_name << " -l -o local_dns_report.json\n";
    std::cout << "  " << program_name << " -d example.com -s 60\n";
}

void printSummary(const dns_health_checker::HealthReport &report)
{
    std::cout << "\n=== DNS Health Check Summary ===\n";
    std::cout << "Domain: " << report.domain << "\n";
    std::cout << "Timestamp: " << dns_health_checker::dns_utils::formatTimestamp(report.timestamp) << "\n";
    std::cout << "Overall Status: " << (report.overall_healthy ? "HEALTHY" : "ISSUES FOUND") << "\n\n";

    for (const auto &check : report.checks)
    {
        std::cout << check.name << ": " << dns_health_checker::dns_utils::resultToString(check.result);
        if (!check.details.empty())
        {
            std::cout << " - " << check.details;
        }
        std::cout << "\n";
    }
    std::cout << "\n";
}

int main(int argc, char *argv[])
{
    CommandLineArgs args = parseArguments(argc, argv);

    if (args.show_help)
    {
        printUsage(argv[0]);
        return 0;
    }

    // Validate arguments
    if (args.domain.empty() && !args.check_local)
    {
        std::cerr << "Error: Must specify either a domain (-d) or local check (-l)\n";
        printUsage(argv[0]);
        return 1;
    }

    if (!args.domain.empty() && args.check_local)
    {
        std::cerr << "Error: Cannot specify both domain and local check\n";
        printUsage(argv[0]);
        return 1;
    }

    try
    {
        dns_health_checker::DNSHealthChecker checker;

        auto performCheck = [&]()
        {
            std::string json_result;

            if (args.check_local)
            {
                std::cout << "Performing local machine DNS health check...\n";
                json_result = checker.checkLocalMachineJson();
            }
            else
            {
                std::cout << "Performing DNS health check for domain: " << args.domain << "\n";
                json_result = checker.checkDomainJson(args.domain);
            }

            // Print JSON to console
            std::cout << "\n=== JSON Result ===\n";
            std::cout << json_result << "\n";

            // Save JSON to file
            std::cout << "Saving JSON report: " << args.output_file << "\n";
            std::ofstream outfile(args.output_file);
            if (outfile.is_open())
            {
                outfile << json_result;
                outfile.close();
                std::cout << "JSON report saved successfully!\n";
            }
            else
            {
                std::cerr << "Error saving JSON report to file\n";
                return false;
            }

            return true;
        };

        // Perform initial check
        if (!performCheck())
        {
            return 1;
        }

        // Schedule recurring checks if requested
        if (args.schedule_minutes > 0)
        {
            std::cout << "Scheduling checks every " << args.schedule_minutes << " minutes...\n";
            std::cout << "Press Ctrl+C to stop.\n";

            while (true)
            {
                std::this_thread::sleep_for(std::chrono::minutes(args.schedule_minutes));
                std::cout << "\n--- Scheduled Check ---\n";
                performCheck();
            }
        }
    }
    catch (const std::exception &e)
    {
        std::cerr << "Error: " << e.what() << "\n";
        return 1;
    }

    return 0;
}
