/* Cybersecurity Scanner Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Styles */
header {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-content h1 {
    color: #2c3e50;
    font-size: 2.5em;
    margin-bottom: 10px;
}

.header-content h1 i {
    color: #e74c3c;
    margin-right: 15px;
}

.header-content p {
    color: #7f8c8d;
    font-size: 1.2em;
}

.help-btn {
    background: #3498db;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1em;
    transition: all 0.3s ease;
}

.help-btn:hover {
    background: #2980b9;
    transform: translateY(-2px);
}

/* Main Content */
main {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

/* Scan Form */
.scan-form {
    margin-bottom: 30px;
}

.input-group {
    display: flex;
    gap: 15px;
    margin-bottom: 25px;
    flex-wrap: wrap;
}

#targetInput {
    flex: 2;
    padding: 15px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1.1em;
    transition: border-color 0.3s ease;
}

#targetInput:focus {
    outline: none;
    border-color: #3498db;
}

#scanType {
    flex: 1;
    padding: 15px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1.1em;
    background: white;
}

#scanButton {
    background: #27ae60;
    color: white;
    border: none;
    padding: 15px 25px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1.1em;
    font-weight: bold;
    transition: all 0.3s ease;
    min-width: 150px;
}

#scanButton:hover {
    background: #229954;
    transform: translateY(-2px);
}

#scanButton:disabled {
    background: #95a5a6;
    cursor: not-allowed;
    transform: none;
}

/* Scan Options */
.scan-options {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #e9ecef;
}

.scan-options h3 {
    margin-bottom: 15px;
    color: #2c3e50;
}

.options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.options-grid label {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 10px;
    border-radius: 5px;
    transition: background-color 0.3s ease;
}

.options-grid label:hover {
    background: rgba(52, 152, 219, 0.1);
}

.options-grid input[type="checkbox"] {
    margin-right: 10px;
    transform: scale(1.2);
}

/* Loading Section */
.loading-section {
    text-align: center;
    padding: 50px;
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 6px solid #f3f3f3;
    border-top: 6px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #f0f0f0;
    border-radius: 10px;
    overflow: hidden;
    margin: 20px 0;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2ecc71);
    width: 0%;
    transition: width 0.3s ease;
}

.scan-progress {
    margin-top: 15px;
    color: #7f8c8d;
}

/* Results Section */
.results-section {
    margin-top: 30px;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #ecf0f1;
}

.results-header h2 {
    color: #2c3e50;
    font-size: 2em;
}

.results-actions {
    display: flex;
    gap: 10px;
}

.export-btn, .clear-btn {
    padding: 10px 15px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9em;
    transition: all 0.3s ease;
}

.export-btn {
    background: #3498db;
    color: white;
}

.export-btn:hover {
    background: #2980b9;
}

.clear-btn {
    background: #e74c3c;
    color: white;
}

.clear-btn:hover {
    background: #c0392b;
}

/* Summary Cards */
.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background: white;
    padding: 25px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    text-align: center;
    border-left: 5px solid #3498db;
}

.summary-card h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.1em;
}

.stat-value {
    font-size: 2.5em;
    font-weight: bold;
    color: #3498db;
    margin-bottom: 5px;
}

.stat-label {
    color: #7f8c8d;
    font-size: 0.9em;
}

/* Risk Score Card */
.risk-score {
    border-left-color: #e74c3c;
}

.score-circle {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    border: 8px solid #ecf0f1;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 1.8em;
    font-weight: bold;
    color: white;
    background: #e74c3c;
}

.score-circle.low { background: #27ae60; border-color: #2ecc71; }
.score-circle.medium { background: #f39c12; border-color: #e67e22; }
.score-circle.high { background: #e74c3c; border-color: #c0392b; }

/* Tabs */
.results-tabs {
    margin-top: 30px;
}

.tab-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    margin-bottom: 20px;
    border-bottom: 2px solid #ecf0f1;
}

.tab-button {
    padding: 12px 20px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 1em;
    color: #7f8c8d;
    border-bottom: 3px solid transparent;
    transition: all 0.3s ease;
}

.tab-button.active {
    color: #3498db;
    border-bottom-color: #3498db;
}

.tab-button:hover {
    color: #2c3e50;
}

.tab-content {
    display: none;
    padding: 20px 0;
}

.tab-content.active {
    display: block;
}

/* Modal Styles */
.modal {
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 10% auto;
    padding: 30px;
    border-radius: 10px;
    width: 80%;
    max-width: 600px;
    position: relative;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.close {
    position: absolute;
    right: 20px;
    top: 15px;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    color: #aaa;
}

.close:hover {
    color: #000;
}

/* Footer */
footer {
    text-align: center;
    padding: 20px;
    color: rgba(255, 255, 255, 0.8);
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }
    
    .input-group {
        flex-direction: column;
    }
    
    .results-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .tab-buttons {
        justify-content: center;
    }
    
    .modal-content {
        width: 95%;
        margin: 5% auto;
    }
}
