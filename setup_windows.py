#!/usr/bin/env python3
"""
Windows-specific setup script for Cybersecurity Scanner
Simplified version that handles Windows dependencies properly
"""

import os
import sys
import subprocess
import platform
import urllib.request
import zipfile
import shutil
from pathlib import Path

def print_status(message, status="INFO"):
    symbols = {"INFO": "ℹ", "SUCCESS": "✓", "ERROR": "✗", "WARNING": "⚠"}
    print(f"{symbols.get(status, 'ℹ')} {message}")

def run_command(command, shell=True):
    """Run a command and return success status"""
    try:
        result = subprocess.run(command, shell=shell, capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def install_python_packages():
    """Install Python packages"""
    print_status("Installing Python packages...")
    
    # Install packages one by one to handle failures gracefully
    packages = [
        "requests",
        "python-nmap", 
        "dnspython",
        "cryptography",
        "beautifulsoup4",
        "lxml",
        "geoip2",
        "python-whois",
        "urllib3",
        "certifi"
    ]
    
    failed_packages = []
    
    for package in packages:
        print(f"  Installing {package}...")
        success, stdout, stderr = run_command([sys.executable, "-m", "pip", "install", package])
        
        if not success:
            print_status(f"Failed to install {package}: {stderr}", "WARNING")
            failed_packages.append(package)
        else:
            print_status(f"Installed {package}", "SUCCESS")
    
    if failed_packages:
        print_status(f"Some packages failed to install: {', '.join(failed_packages)}", "WARNING")
        print_status("You can try installing them manually with: pip install <package_name>", "INFO")
    else:
        print_status("All Python packages installed successfully", "SUCCESS")
    
    return len(failed_packages) == 0

def setup_nlohmann_json():
    """Download and setup nlohmann/json library"""
    print_status("Setting up nlohmann/json library...")
    
    include_dir = Path("include/nlohmann")
    include_dir.mkdir(parents=True, exist_ok=True)
    
    json_header = include_dir / "json.hpp"
    
    if json_header.exists():
        print_status("nlohmann/json already exists", "SUCCESS")
        return True
    
    try:
        url = "https://github.com/nlohmann/json/releases/download/v3.11.2/json.hpp"
        print_status(f"Downloading from {url}...")
        
        urllib.request.urlretrieve(url, json_header)
        print_status("nlohmann/json downloaded successfully", "SUCCESS")
        return True
        
    except Exception as e:
        print_status(f"Failed to download nlohmann/json: {e}", "ERROR")
        return False

def check_nmap():
    """Check if nmap is available"""
    print_status("Checking for nmap...")
    
    success, stdout, stderr = run_command("nmap --version")
    
    if success:
        print_status("nmap is available", "SUCCESS")
        return True
    else:
        print_status("nmap not found", "WARNING")
        print_status("Please download and install nmap from: https://nmap.org/download.html", "INFO")
        print_status("Make sure to add nmap to your system PATH", "INFO")
        return False

def create_directories():
    """Create necessary directories"""
    print_status("Creating directories...")
    
    directories = ["results", "data", "build", "bin"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print_status(f"Created directory: {directory}", "SUCCESS")

def create_build_script():
    """Create Windows build script"""
    print_status("Creating build script...")
    
    build_script = """@echo off
echo Building Cybersecurity Scanner...

REM Create build directory
if not exist build mkdir build
cd build

REM Configure with CMake
cmake .. -DCMAKE_BUILD_TYPE=Release
if %ERRORLEVEL% neq 0 (
    echo CMake configuration failed
    pause
    exit /b 1
)

REM Build the project
cmake --build . --config Release
if %ERRORLEVEL% neq 0 (
    echo Build failed
    pause
    exit /b 1
)

echo Build completed successfully!
cd ..
pause
"""
    
    with open("build.bat", "w") as f:
        f.write(build_script)
    
    print_status("Created build.bat", "SUCCESS")

def create_run_script():
    """Create script to run the web server"""
    print_status("Creating run script...")
    
    run_script = """@echo off
echo Starting Cybersecurity Scanner Web Server...

REM Check if Python is available
python --version >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo Python is not available or not in PATH
    pause
    exit /b 1
)

REM Start the web server
python web_server.py --open-browser

pause
"""
    
    with open("run_scanner.bat", "w") as f:
        f.write(run_script)
    
    print_status("Created run_scanner.bat", "SUCCESS")

def main():
    """Main setup function"""
    print("=== Cybersecurity Scanner Windows Setup ===")
    print(f"Platform: {platform.system()} {platform.release()}")
    print(f"Python: {sys.version}")
    print()
    
    success_count = 0
    total_steps = 6
    
    # Step 1: Install Python packages
    if install_python_packages():
        success_count += 1
    
    # Step 2: Setup nlohmann/json
    if setup_nlohmann_json():
        success_count += 1
    
    # Step 3: Check nmap
    if check_nmap():
        success_count += 1
    
    # Step 4: Create directories
    create_directories()
    success_count += 1
    
    # Step 5: Create build script
    create_build_script()
    success_count += 1
    
    # Step 6: Create run script
    create_run_script()
    success_count += 1
    
    print()
    print("=== Setup Summary ===")
    print(f"Completed {success_count}/{total_steps} steps successfully")
    
    if success_count == total_steps:
        print_status("Setup completed successfully!", "SUCCESS")
        print()
        print("Next steps:")
        print("1. Run 'build.bat' to compile the C++ components")
        print("2. Run 'run_scanner.bat' to start the web interface")
        print("3. Or use 'python web_server.py --open-browser' directly")
    else:
        print_status("Setup completed with some issues", "WARNING")
        print()
        print("You may need to:")
        print("1. Install missing Python packages manually")
        print("2. Download and install nmap from https://nmap.org/download.html")
        print("3. Install Visual Studio Build Tools for C++ compilation")

if __name__ == "__main__":
    main()
