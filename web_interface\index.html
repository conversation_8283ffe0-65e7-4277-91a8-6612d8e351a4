<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cybersecurity Scanner</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header>
            <div class="header-content">
                <h1><i class="fas fa-shield-alt"></i> Cybersecurity Scanner</h1>
                <p>Comprehensive security analysis for domains and IP addresses</p>
            </div>
            <div class="header-actions">
                <button onclick="openModal('helpModal')" class="help-btn">
                    <i class="fas fa-question-circle"></i> Help
                </button>
            </div>
        </header>

        <main>
            <div class="scan-form">
                <div class="input-group">
                    <input type="text" id="targetInput" placeholder="Enter domain or IP address (e.g., example.com or ***********)" />
                    <select id="scanType">
                        <option value="comprehensive">Comprehensive Scan</option>
                        <option value="quick">Quick Scan</option>
                        <option value="deep">Deep Scan</option>
                    </select>
                    <button id="scanButton" onclick="startScan()">
                        <i class="fas fa-search"></i> Start Scan
                    </button>
                </div>
                
                <div class="scan-options">
                    <h3>Scan Options</h3>
                    <div class="options-grid">
                        <label><input type="checkbox" id="dnsCheck" checked> DNS Health Check</label>
                        <label><input type="checkbox" id="portScan" checked> Port Scanning</label>
                        <label><input type="checkbox" id="sslCheck" checked> SSL/TLS Analysis</label>
                        <label><input type="checkbox" id="httpSecurity" checked> HTTP Security Headers</label>
                        <label><input type="checkbox" id="emailSecurity" checked> Email Security (SPF/DKIM/DMARC)</label>
                        <label><input type="checkbox" id="geoipLookup" checked> GeoIP Lookup</label>
                    </div>
                </div>
            </div>

            <div id="loadingSection" class="loading-section" style="display: none;">
                <div class="loading-spinner"></div>
                <p>Scanning in progress... This may take a few minutes.</p>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div id="scanProgress" class="scan-progress"></div>
            </div>

            <div id="resultsSection" class="results-section" style="display: none;">
                <div class="results-header">
                    <h2>Scan Results</h2>
                    <div class="results-actions">
                        <button onclick="exportResults('json')" class="export-btn">
                            <i class="fas fa-download"></i> Export JSON
                        </button>
                        <button onclick="exportResults('pdf')" class="export-btn">
                            <i class="fas fa-file-pdf"></i> Export PDF
                        </button>
                        <button onclick="clearResults()" class="clear-btn">
                            <i class="fas fa-trash"></i> Clear
                        </button>
                    </div>
                </div>

                <div class="summary-cards">
                    <div class="summary-card risk-score">
                        <h3>Risk Score</h3>
                        <div class="score-display">
                            <div class="score-circle" id="riskScoreCircle">
                                <span id="riskScoreValue">0</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="summary-card">
                        <h3>Open Ports</h3>
                        <div class="stat-value" id="openPortsCount">0</div>
                        <div class="stat-label">ports discovered</div>
                    </div>
                    
                    <div class="summary-card">
                        <h3>Security Issues</h3>
                        <div class="stat-value" id="securityIssuesCount">0</div>
                        <div class="stat-label">issues found</div>
                    </div>
                    
                    <div class="summary-card">
                        <h3>Scan Duration</h3>
                        <div class="stat-value" id="scanDuration">0s</div>
                        <div class="stat-label">total time</div>
                    </div>
                </div>

                <div class="results-tabs">
                    <div class="tab-buttons">
                        <button class="tab-button active" onclick="showTab('overview')">Overview</button>
                        <button class="tab-button" onclick="showTab('dns')">DNS Health</button>
                        <button class="tab-button" onclick="showTab('ports')">Port Scan</button>
                        <button class="tab-button" onclick="showTab('ssl')">SSL/TLS</button>
                        <button class="tab-button" onclick="showTab('http')">HTTP Security</button>
                        <button class="tab-button" onclick="showTab('email')">Email Security</button>
                        <button class="tab-button" onclick="showTab('geoip')">GeoIP</button>
                    </div>

                    <div id="overviewTab" class="tab-content active">
                        <div class="overview-content">
                            <div class="target-info">
                                <h3>Target Information</h3>
                                <div id="targetInfo"></div>
                            </div>
                            
                            <div class="recommendations">
                                <h3>Security Recommendations</h3>
                                <ul id="recommendationsList"></ul>
                            </div>
                            
                            <div class="issues-summary">
                                <h3>Issues Summary</h3>
                                <div id="issuesSummary"></div>
                            </div>
                        </div>
                    </div>

                    <div id="dnsTab" class="tab-content">
                        <div class="dns-results">
                            <h3>DNS Health Check Results</h3>
                            <div id="dnsResults"></div>
                        </div>
                    </div>

                    <div id="portsTab" class="tab-content">
                        <div class="port-results">
                            <h3>Port Scan Results</h3>
                            <div id="portResults"></div>
                        </div>
                    </div>

                    <div id="sslTab" class="tab-content">
                        <div class="ssl-results">
                            <h3>SSL/TLS Analysis Results</h3>
                            <div id="sslResults"></div>
                        </div>
                    </div>

                    <div id="httpTab" class="tab-content">
                        <div class="http-results">
                            <h3>HTTP Security Headers</h3>
                            <div id="httpResults"></div>
                        </div>
                    </div>

                    <div id="emailTab" class="tab-content">
                        <div class="email-results">
                            <h3>Email Security Configuration</h3>
                            <div id="emailResults"></div>
                        </div>
                    </div>

                    <div id="geoipTab" class="tab-content">
                        <div class="geoip-results">
                            <h3>GeoIP and Network Information</h3>
                            <div id="geoipResults"></div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <footer>
            <p>&copy; 2024 Cybersecurity Scanner. Built with C++ and Python.</p>
        </footer>
    </div>

    <!-- Error Modal -->
    <div id="errorModal" class="modal" style="display: none;">
        <div class="modal-content">
            <span class="close" onclick="closeModal('errorModal')">&times;</span>
            <h3><i class="fas fa-exclamation-triangle"></i> Error</h3>
            <p id="errorMessage"></p>
        </div>
    </div>

    <!-- Help Modal -->
    <div id="helpModal" class="modal" style="display: none;">
        <div class="modal-content">
            <span class="close" onclick="closeModal('helpModal')">&times;</span>
            <h3><i class="fas fa-question-circle"></i> Help</h3>
            <div class="help-content">
                <h4>How to use the Cybersecurity Scanner:</h4>
                <ol>
                    <li>Enter a domain name (e.g., example.com) or IP address</li>
                    <li>Select the type of scan you want to perform</li>
                    <li>Choose which security checks to include</li>
                    <li>Click "Start Scan" and wait for results</li>
                </ol>

                <h4>Scan Types:</h4>
                <ul>
                    <li><strong>Quick Scan:</strong> Essential security checks only</li>
                    <li><strong>Comprehensive Scan:</strong> All security checks (recommended)</li>
                    <li><strong>Deep Scan:</strong> Most thorough analysis with additional checks</li>
                </ul>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
