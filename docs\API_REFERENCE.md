# API Reference

The Cybersecurity Scanner provides a RESTful API for programmatic access to scanning functionality.

## Base URL
```
http://localhost:8080/api
```

## Authentication
Currently, no authentication is required for API access. This is suitable for local development and testing environments.

## Endpoints

### 1. Start Scan

**Endpoint:** `POST /api/scan`

**Description:** Initiates a new security scan for the specified target.

**Request Headers:**
```
Content-Type: application/json
```

**Request Body:**
```json
{
  "target": "example.com",
  "scan_type": "quick"
}
```

**Parameters:**
- `target` (string, required): Domain name or IP address to scan
- `scan_type` (string, optional): Type of scan to perform
  - `"quick"` (default): DNS, ports, SSL, GeoIP
  - `"comprehensive"`: Includes HTTP headers, email security
  - `"deep"`: Extended port range, detailed analysis

**Response:**
```json
{
  "status": "success",
  "scan_id": "scan_1751383394",
  "message": "<PERSON><PERSON> initiated successfully",
  "estimated_duration": "60-90 seconds"
}
```

**Error Response:**
```json
{
  "status": "error",
  "message": "Invalid target format"
}
```

### 2. Check Scan Status

**Endpoint:** `GET /api/status`

**Description:** Retrieves the current status of a running scan.

**Query Parameters:**
- `scan_id` (string, required): The scan ID returned from the start scan endpoint

**Example Request:**
```
GET /api/status?scan_id=scan_1751383394
```

**Response (Running):**
```json
{
  "status": "running",
  "scan_id": "scan_1751383394",
  "progress": {
    "current_step": "Port Scanning",
    "completed_steps": ["DNS Health Check"],
    "total_steps": 5,
    "percentage": 40
  },
  "elapsed_time": "45.2 seconds"
}
```

**Response (Completed):**
```json
{
  "status": "completed",
  "scan_id": "scan_1751383394",
  "completion_time": "2025-07-01T12:05:30.123456",
  "total_duration": "66.35 seconds"
}
```

**Response (Error):**
```json
{
  "status": "error",
  "scan_id": "scan_1751383394",
  "error_message": "DNS resolution failed for target"
}
```

### 3. Get Scan Results

**Endpoint:** `GET /api/results`

**Description:** Retrieves the complete results of a completed scan.

**Query Parameters:**
- `scan_id` (string, required): The scan ID of the completed scan

**Example Request:**
```
GET /api/results?scan_id=scan_1751383394
```

**Response:**
```json
{
  "scan_id": "scan_1751383394",
  "target": "example.com",
  "timestamp": "2025-07-01T12:00:00.000000",
  "scan_options": {
    "dns_check": true,
    "port_scan": true,
    "ssl_check": true,
    "http_security": false,
    "email_security": false,
    "geoip_lookup": true
  },
  "results": {
    "dns_health": {
      "status": "healthy",
      "records": {
        "A": ["*************"],
        "AAAA": ["2606:2800:220:1:248:1893:25c8:1946"],
        "MX": [{"priority": 0, "exchange": "."}],
        "NS": ["a.iana-servers.net.", "b.iana-servers.net."]
      },
      "response_times": {
        "A": 45.2,
        "AAAA": 52.1,
        "MX": 48.7,
        "NS": 41.3
      }
    },
    "port_scan": {
      "open_ports": [80, 443],
      "closed_ports": [],
      "filtered_ports": [],
      "port_details": {
        "80": {
          "service": "http",
          "banner": "nginx/1.18.0",
          "state": "open"
        },
        "443": {
          "service": "https",
          "banner": "nginx/1.18.0",
          "state": "open"
        }
      }
    },
    "ssl_tls": {
      "certificate_valid": true,
      "certificate_expiry": "2025-12-31T23:59:59Z",
      "certificate_issuer": "DigiCert Inc",
      "certificate_subject": "example.com",
      "ssl_version": "TLSv1.3",
      "cipher_suite": "TLS_AES_256_GCM_SHA384",
      "hsts_enabled": true,
      "security_issues": []
    },
    "geoip": {
      "ip": "*************",
      "country": "United States",
      "region": "Virginia",
      "city": "Ashburn",
      "organization": "Edgecast Inc.",
      "asn": "AS15133"
    }
  },
  "summary": {
    "total_checks": 4,
    "successful_checks": 4,
    "failed_checks": 0,
    "security_issues": [],
    "recommendations": [
      "Consider implementing additional security headers",
      "Regular certificate monitoring recommended"
    ],
    "risk_score": 2,
    "open_ports": [80, 443],
    "ssl_issues": [],
    "email_issues": []
  }
}
```

### 4. List Recent Scans

**Endpoint:** `GET /api/scans`

**Description:** Retrieves a list of recent scans.

**Query Parameters:**
- `limit` (integer, optional): Maximum number of scans to return (default: 10)
- `offset` (integer, optional): Number of scans to skip (default: 0)

**Response:**
```json
{
  "scans": [
    {
      "scan_id": "scan_1751383394",
      "target": "example.com",
      "scan_type": "quick",
      "status": "completed",
      "timestamp": "2025-07-01T12:00:00.000000",
      "duration": "66.35 seconds"
    }
  ],
  "total": 1,
  "limit": 10,
  "offset": 0
}
```

## Error Codes

| HTTP Status | Error Code | Description |
|-------------|------------|-------------|
| 400 | INVALID_TARGET | Target format is invalid |
| 400 | INVALID_SCAN_TYPE | Scan type is not supported |
| 404 | SCAN_NOT_FOUND | Scan ID does not exist |
| 500 | SCAN_FAILED | Internal error during scanning |
| 500 | SERVER_ERROR | General server error |

## Rate Limiting

Currently, no rate limiting is implemented. For production use, consider implementing:
- Maximum concurrent scans per client
- Request rate limiting
- Resource usage monitoring

## Example Usage

### Python Example
```python
import requests
import time

# Start scan
response = requests.post('http://localhost:8080/api/scan', 
                        json={'target': 'example.com', 'scan_type': 'quick'})
scan_data = response.json()
scan_id = scan_data['scan_id']

# Poll for completion
while True:
    status_response = requests.get(f'http://localhost:8080/api/status?scan_id={scan_id}')
    status_data = status_response.json()
    
    if status_data['status'] == 'completed':
        break
    elif status_data['status'] == 'error':
        print(f"Scan failed: {status_data['error_message']}")
        exit(1)
    
    time.sleep(5)

# Get results
results_response = requests.get(f'http://localhost:8080/api/results?scan_id={scan_id}')
results = results_response.json()
print(f"Scan completed with risk score: {results['summary']['risk_score']}")
```

### JavaScript Example
```javascript
async function performScan(target, scanType = 'quick') {
    // Start scan
    const scanResponse = await fetch('/api/scan', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ target, scan_type: scanType })
    });
    
    const scanData = await scanResponse.json();
    const scanId = scanData.scan_id;
    
    // Poll for completion
    while (true) {
        const statusResponse = await fetch(`/api/status?scan_id=${scanId}`);
        const statusData = await statusResponse.json();
        
        if (statusData.status === 'completed') {
            break;
        } else if (statusData.status === 'error') {
            throw new Error(`Scan failed: ${statusData.error_message}`);
        }
        
        await new Promise(resolve => setTimeout(resolve, 5000));
    }
    
    // Get results
    const resultsResponse = await fetch(`/api/results?scan_id=${scanId}`);
    return await resultsResponse.json();
}
```

## WebSocket Support (Future Enhancement)

For real-time progress updates, consider implementing WebSocket connections:
```javascript
const ws = new WebSocket(`ws://localhost:8080/ws/scan/${scanId}`);
ws.onmessage = (event) => {
    const progress = JSON.parse(event.data);
    console.log(`Progress: ${progress.percentage}%`);
};
```
