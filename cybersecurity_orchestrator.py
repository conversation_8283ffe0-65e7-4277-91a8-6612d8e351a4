#!/usr/bin/env python3
"""
Cybersecurity Scanner Orchestrator
Coordinates C++ DNS/network components with Python security modules
"""

import json
import subprocess
import datetime
import os
import sys
import argparse
import threading
import time
from pathlib import Path

# Import Python security modules
sys.path.append('python_modules')
from ssl_checker import <PERSON><PERSON>he<PERSON>
from http_security import HTTPSecurity<PERSON>he<PERSON>
from port_scanner import PortScanner
from email_security import EmailSecurityChecker
from geoip_lookup import GeoIPLookup

class CybersecurityOrchestrator:
    def __init__(self, config_file="config.json"):
        self.config = self._load_config(config_file)
        self.results_dir = Path(self.config.get("output", {}).get("results_dir", "results"))
        self.results_dir.mkdir(exist_ok=True)
        
        # Initialize security modules
        self.ssl_checker = SSLChecker()
        self.http_checker = HTTPSecurityChecker()
        self.port_scanner = PortScanner()
        self.email_checker = EmailSecurityChecker()
        self.geoip_lookup = GeoIPLookup(self.config.get("tools", {}).get("geoip_db", "data/GeoLite2-City.mmdb"))
        
        # C++ DNS checker path
        self.dns_checker_path = self._find_dns_checker()
    
    def _load_config(self, config_file):
        """Load configuration from JSON file"""
        default_config = {
            "tools": {
                "nmap_path": "nmap",
                "masscan_path": "masscan",
                "geoip_db": "data/GeoLite2-City.mmdb"
            },
            "scanning": {
                "default_ports": "1-1000",
                "timeout": 30,
                "max_threads": 50
            },
            "output": {
                "format": "json",
                "save_results": True,
                "results_dir": "results"
            }
        }
        
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    config = json.load(f)
                # Merge with defaults
                for key, value in default_config.items():
                    if key not in config:
                        config[key] = value
                    elif isinstance(value, dict):
                        for subkey, subvalue in value.items():
                            if subkey not in config[key]:
                                config[key][subkey] = subvalue
                return config
            except Exception as e:
                print(f"Warning: Could not load config file {config_file}: {e}")
        
        return default_config
    
    def _find_dns_checker(self):
        """Find the C++ DNS checker executable"""
        possible_paths = [
            "dns_health_checker",
            "dns_health_checker.exe",
            "build/bin/dns_health_checker",
            "build/bin/dns_health_checker.exe",
            "bin/dns_health_checker",
            "bin/dns_health_checker.exe"
        ]
        
        for path in possible_paths:
            if os.path.exists(path) and os.access(path, os.X_OK):
                return path
        
        return None
    
    def run_dns_check(self, target):
        """Run DNS health checker (C++ if available, Python fallback)"""
        # Try C++ DNS checker first
        if self.dns_checker_path:
            try:
                # Run DNS checker and capture JSON output
                cmd = [self.dns_checker_path, target, "--output", "temp_dns_result.json"]
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)

                if result.returncode == 0:
                    # Read the JSON output
                    if os.path.exists("temp_dns_result.json"):
                        with open("temp_dns_result.json", 'r') as f:
                            dns_result = json.load(f)
                        os.remove("temp_dns_result.json")
                        return dns_result
                    else:
                        # Fallback: try to parse stdout as JSON
                        try:
                            return json.loads(result.stdout)
                        except:
                            pass  # Fall through to Python implementation

            except (subprocess.TimeoutExpired, Exception):
                pass  # Fall through to Python implementation

        # Python DNS fallback implementation
        return self._python_dns_check(target)

    def _python_dns_check(self, target):
        """Python-based DNS health check"""
        import dns.resolver
        import dns.reversename
        import time

        result = {
            "target": target,
            "timestamp": datetime.datetime.now().isoformat(),
            "overall_healthy": True,
            "checks": [],
            "recommendations": [],
            "dns_servers": [],
            "response_times": {}
        }

        try:
            # A Record Check
            start_time = time.time()
            try:
                a_records = dns.resolver.resolve(target, 'A')
                response_time = (time.time() - start_time) * 1000
                result["response_times"]["A"] = response_time

                ips = [str(record) for record in a_records]
                result["checks"].append({
                    "name": "A Record Resolution",
                    "status": "PASS",
                    "details": f"Resolved to {', '.join(ips)} in {response_time:.1f}ms",
                    "response_time_ms": response_time,
                    "records": ips
                })
            except Exception as e:
                result["checks"].append({
                    "name": "A Record Resolution",
                    "status": "FAIL",
                    "details": f"Failed to resolve A record: {str(e)}"
                })
                result["overall_healthy"] = False

            # AAAA Record Check (IPv6)
            try:
                aaaa_records = dns.resolver.resolve(target, 'AAAA')
                ipv6s = [str(record) for record in aaaa_records]
                result["checks"].append({
                    "name": "AAAA Record Resolution",
                    "status": "PASS",
                    "details": f"IPv6 addresses: {', '.join(ipv6s)}",
                    "records": ipv6s
                })
            except dns.resolver.NXDOMAIN:
                result["checks"].append({
                    "name": "AAAA Record Resolution",
                    "status": "INFO",
                    "details": "No IPv6 (AAAA) records found"
                })
            except Exception as e:
                result["checks"].append({
                    "name": "AAAA Record Resolution",
                    "status": "WARN",
                    "details": f"IPv6 check failed: {str(e)}"
                })

            # MX Record Check
            try:
                mx_records = dns.resolver.resolve(target, 'MX')
                mx_list = [f"{record.preference} {record.exchange}" for record in mx_records]
                result["checks"].append({
                    "name": "MX Record Check",
                    "status": "PASS",
                    "details": f"Mail servers: {', '.join(mx_list)}",
                    "records": mx_list
                })
            except dns.resolver.NXDOMAIN:
                result["checks"].append({
                    "name": "MX Record Check",
                    "status": "INFO",
                    "details": "No MX records found (no mail service)"
                })
            except Exception as e:
                result["checks"].append({
                    "name": "MX Record Check",
                    "status": "WARN",
                    "details": f"MX check failed: {str(e)}"
                })

            # NS Record Check
            try:
                ns_records = dns.resolver.resolve(target, 'NS')
                ns_list = [str(record) for record in ns_records]
                result["checks"].append({
                    "name": "NS Record Check",
                    "status": "PASS",
                    "details": f"Name servers: {', '.join(ns_list)}",
                    "records": ns_list
                })
            except Exception as e:
                result["checks"].append({
                    "name": "NS Record Check",
                    "status": "FAIL",
                    "details": f"NS check failed: {str(e)}"
                })
                result["overall_healthy"] = False

            # Add recommendations
            if result["response_times"].get("A", 0) > 1000:
                result["recommendations"].append("DNS response time is slow (>1s), consider using faster DNS servers")

            if not any(check["name"] == "AAAA Record Resolution" and check["status"] == "PASS" for check in result["checks"]):
                result["recommendations"].append("Consider adding IPv6 (AAAA) records for better connectivity")

        except Exception as e:
            result["checks"].append({
                "name": "DNS Health Check",
                "status": "FAIL",
                "details": f"DNS check failed: {str(e)}"
            })
            result["overall_healthy"] = False

        return result
    
    def comprehensive_scan(self, target, scan_options=None):
        """Perform comprehensive cybersecurity scan"""
        if scan_options is None:
            scan_options = {
                "dns_check": True,
                "port_scan": True,
                "ssl_check": True,
                "http_security": True,
                "email_security": True,
                "geoip_lookup": True
            }
        
        scan_id = f"scan_{int(time.time())}"
        result = {
            "scan_id": scan_id,
            "target": target,
            "timestamp": datetime.datetime.now().isoformat(),
            "scan_options": scan_options,
            "results": {},
            "summary": {},
            "duration": 0
        }
        
        start_time = time.time()
        
        print(f"Starting comprehensive scan for {target}...")
        
        # Determine if target is domain or IP
        is_domain = not self._is_ip_address(target)
        
        # DNS Health Check (C++ component)
        if scan_options.get("dns_check", True):
            print("Running DNS health check...")
            result["results"]["dns_health"] = self.run_dns_check(target)
        
        # Port Scanning
        if scan_options.get("port_scan", True):
            print("Running port scan...")
            ports = self.config["scanning"]["default_ports"]
            if self.port_scanner.nmap_available:
                result["results"]["port_scan"] = self.port_scanner.scan_ports_nmap(target, ports)
            else:
                result["results"]["port_scan"] = self.port_scanner.scan_ports_custom(target, ports)
        
        # SSL/TLS Check
        if scan_options.get("ssl_check", True) and is_domain:
            print("Running SSL/TLS check...")
            result["results"]["ssl_tls"] = self.ssl_checker.check_ssl_certificate(target)
            
            # Also check HTTPS URL
            https_url = f"https://{target}"
            result["results"]["hsts"] = self.ssl_checker.check_hsts_header(https_url)
        
        # HTTP Security Headers
        if scan_options.get("http_security", True) and is_domain:
            print("Running HTTP security check...")
            http_url = f"http://{target}"
            https_url = f"https://{target}"
            
            result["results"]["http_security"] = {
                "http": self.http_checker.check_security_headers(http_url),
                "https": self.http_checker.check_security_headers(https_url)
            }
            
            # Check for directory listing vulnerabilities
            result["results"]["directory_listing"] = self.http_checker.check_directory_listing(https_url)
            
            # Get favicon hash for fingerprinting
            result["results"]["favicon"] = self.http_checker.get_favicon_hash(https_url)
        
        # Email Security Check
        if scan_options.get("email_security", True) and is_domain:
            print("Running email security check...")
            result["results"]["email_security"] = self.email_checker.comprehensive_email_check(target)
        
        # GeoIP Lookup
        if scan_options.get("geoip_lookup", True):
            print("Running GeoIP lookup...")
            if is_domain:
                result["results"]["geoip"] = self.geoip_lookup.comprehensive_domain_lookup(target)
            else:
                result["results"]["geoip"] = self.geoip_lookup.lookup_ip(target)
        
        # Calculate scan duration
        result["duration"] = time.time() - start_time
        
        # Generate summary
        result["summary"] = self._generate_scan_summary(result["results"])
        
        # Save results
        if self.config["output"]["save_results"]:
            self._save_scan_results(result)
        
        print(f"Scan completed in {result['duration']:.2f} seconds")
        return result
    
    def _is_ip_address(self, target):
        """Check if target is an IP address"""
        try:
            import ipaddress
            ipaddress.ip_address(target)
            return True
        except ValueError:
            return False
    
    def _generate_scan_summary(self, results):
        """Generate summary of scan results"""
        summary = {
            "total_checks": 0,
            "successful_checks": 0,
            "failed_checks": 0,
            "security_issues": [],
            "recommendations": [],
            "risk_score": 0,
            "open_ports": [],
            "ssl_issues": [],
            "email_issues": []
        }
        
        # Count checks
        for check_name, check_result in results.items():
            summary["total_checks"] += 1
            
            if isinstance(check_result, dict) and "error" not in check_result:
                summary["successful_checks"] += 1
            else:
                summary["failed_checks"] += 1
        
        # Extract security issues
        if "port_scan" in results and "open_ports" in results["port_scan"]:
            summary["open_ports"] = [p["port"] for p in results["port_scan"]["open_ports"]]
        
        if "ssl_tls" in results and "security_issues" in results["ssl_tls"]:
            summary["ssl_issues"] = results["ssl_tls"]["security_issues"]
        
        if "email_security" in results and "summary" in results["email_security"]:
            email_summary = results["email_security"]["summary"]
            summary["email_issues"] = email_summary.get("recommendations", [])
        
        # Calculate risk score (0-100)
        risk_score = 0
        
        # Port scan risks
        if len(summary["open_ports"]) > 10:
            risk_score += 20
        elif len(summary["open_ports"]) > 5:
            risk_score += 10
        
        # SSL risks
        if summary["ssl_issues"]:
            risk_score += len(summary["ssl_issues"]) * 5
        
        # Email security risks
        if summary["email_issues"]:
            risk_score += len(summary["email_issues"]) * 3
        
        summary["risk_score"] = min(risk_score, 100)
        
        return summary
    
    def _save_scan_results(self, result):
        """Save scan results to file"""
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"scan_{result['target']}_{timestamp}.json"
        filepath = self.results_dir / filename
        
        try:
            with open(filepath, 'w') as f:
                json.dump(result, f, indent=2)
            print(f"Results saved to {filepath}")
        except Exception as e:
            print(f"Failed to save results: {e}")
    
    def quick_scan(self, target):
        """Perform quick scan with essential checks only"""
        scan_options = {
            "dns_check": True,
            "port_scan": True,
            "ssl_check": True,
            "http_security": False,
            "email_security": False,
            "geoip_lookup": True
        }
        
        return self.comprehensive_scan(target, scan_options)
    
    def deep_scan(self, target):
        """Perform deep scan with all available checks"""
        scan_options = {
            "dns_check": True,
            "port_scan": True,
            "ssl_check": True,
            "http_security": True,
            "email_security": True,
            "geoip_lookup": True
        }
        
        return self.comprehensive_scan(target, scan_options)

def main():
    """Main function for command-line usage"""
    parser = argparse.ArgumentParser(description="Cybersecurity Scanner Orchestrator")
    parser.add_argument("target", help="Target domain or IP address to scan")
    parser.add_argument("--scan-type", choices=["quick", "comprehensive", "deep"], 
                       default="comprehensive", help="Type of scan to perform")
    parser.add_argument("--config", default="config.json", help="Configuration file path")
    parser.add_argument("--output", help="Output file path (optional)")
    parser.add_argument("--no-save", action="store_true", help="Don't save results to file")
    
    args = parser.parse_args()
    
    # Initialize orchestrator
    orchestrator = CybersecurityOrchestrator(args.config)
    
    # Override save setting if requested
    if args.no_save:
        orchestrator.config["output"]["save_results"] = False
    
    # Perform scan based on type
    if args.scan_type == "quick":
        result = orchestrator.quick_scan(args.target)
    elif args.scan_type == "deep":
        result = orchestrator.deep_scan(args.target)
    else:
        result = orchestrator.comprehensive_scan(args.target)
    
    # Output results
    if args.output:
        with open(args.output, 'w') as f:
            json.dump(result, f, indent=2)
        print(f"Results saved to {args.output}")
    else:
        print(json.dumps(result, indent=2))

if __name__ == "__main__":
    main()
