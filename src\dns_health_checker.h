#ifndef DNS_HEALTH_CHECKER_H
#define DNS_HEALTH_CHECKER_H

#include <string>
#include <vector>
#include <map>
#include <memory>
#include <chrono>
#include <nlohmann/json.hpp>

// Forward declarations
struct ldns_resolver;
struct ldns_rdf;
struct ldns_rr_list;

namespace dns_health_checker
{

    // Enumeration for check results
    enum class CheckResult
    {
        PASS,
        FAIL,
        WARNING,
        NOT_APPLICABLE
    };

    // Structure to hold individual check results
    struct CheckInfo
    {
        std::string name;
        CheckResult result;
        std::string details;
        std::vector<std::string> recommendations;

        CheckInfo(const std::string &n, CheckResult r, const std::string &d = "")
            : name(n), result(r), details(d) {}

        // Convert to JSON
        nlohmann::json toJson() const;
    };

    // Structure to hold complete domain health report
    struct HealthReport
    {
        std::string domain;
        std::chrono::system_clock::time_point timestamp;
        std::vector<CheckInfo> checks;
        bool overall_healthy;

        HealthReport(const std::string &d)
            : domain(d), timestamp(std::chrono::system_clock::now()), overall_healthy(true) {}

        // Convert to JSON
        nlohmann::json toJson() const;
        std::string toJsonString() const;
    };

    // Main DNS Health Checker class
    class DNSHealthChecker
    {
    public:
        DNSHealthChecker();
        ~DNSHealthChecker();

        // Main checking functions
        HealthReport checkDomain(const std::string &domain);
        HealthReport checkLocalMachine();

        // JSON output functions
        std::string checkDomainJson(const std::string &domain);
        std::string checkLocalMachineJson();

        // Individual check functions
        CheckInfo checkSPF(const std::string &domain);
        CheckInfo checkDKIM(const std::string &domain, const std::string &selector = "default");
        CheckInfo checkDMARC(const std::string &domain);
        CheckInfo checkMX(const std::string &domain);
        CheckInfo checkPTR(const std::string &domain);
        CheckInfo checkDNSSEC(const std::string &domain);
        CheckInfo checkTTL(const std::string &domain);

        // Utility functions
        bool isValidDomain(const std::string &domain);
        std::string getLocalHostname();

    private:
        ldns_resolver *resolver_;

        // Helper functions
        bool initializeResolver();
        void cleanupResolver();
        std::vector<std::string> queryTXT(const std::string &domain);
        std::vector<std::string> queryMX(const std::string &domain);
        bool queryDNSSEC(const std::string &domain);
        uint32_t queryTTL(const std::string &domain, const std::string &record_type);

        // SPF specific helpers
        bool validateSPFSyntax(const std::string &spf_record);
        bool isPermissiveSPF(const std::string &spf_record);

        // DKIM specific helpers
        std::vector<std::string> getCommonDKIMSelectors();
        bool validateDKIMRecord(const std::string &dkim_record);

        // DMARC specific helpers
        bool validateDMARCSyntax(const std::string &dmarc_record);
        std::string extractDMARCPolicy(const std::string &dmarc_record);

        // MX specific helpers
        bool validateMXPriorities(const std::vector<std::string> &mx_records);

        // TTL specific helpers
        bool isTTLOptimal(uint32_t ttl, const std::string &record_type);

        // Error handling
        std::string getLastError() const;
        void setLastError(const std::string &error);

        std::string last_error_;
    };

    // DNS Checker utility functions
    namespace dns_utils
    {
        std::string resultToString(CheckResult result);
        std::string formatTimestamp(const std::chrono::system_clock::time_point &tp);
        bool isValidIPv4(const std::string &ip);
        bool isValidIPv6(const std::string &ip);
        std::string extractDomainFromEmail(const std::string &email);

        // JSON conversion utilities
        CheckResult stringToCheckResult(const std::string &str);
        nlohmann::json checkResultToJson(CheckResult result);
        CheckResult jsonToCheckResult(const nlohmann::json &j);
    }

} // namespace dns_health_checker

#endif // DNS_HEALTH_CHECKER_H
