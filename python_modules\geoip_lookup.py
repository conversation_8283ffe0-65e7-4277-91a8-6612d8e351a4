#!/usr/bin/env python3
"""
GeoIP and ASN Lookup Module
Provides IP geolocation and autonomous system number information
"""

import json
import datetime
import requests
import socket
import ipaddress
import geoip2.database
import geoip2.errors
import os

class GeoIPLookup:
    def __init__(self, geoip_db_path="data/GeoLite2-City.mmdb"):
        self.geoip_db_path = geoip_db_path
        self.geoip_reader = None
        self.timeout = 10
        
        # Try to initialize GeoIP database
        if os.path.exists(geoip_db_path):
            try:
                self.geoip_reader = geoip2.database.Reader(geoip_db_path)
            except Exception as e:
                print(f"Warning: Could not load GeoIP database: {e}")
    
    def lookup_ip(self, ip_address):
        """Perform comprehensive IP lookup"""
        result = {
            "ip_address": ip_address,
            "timestamp": datetime.datetime.now().isoformat(),
            "is_private": False,
            "is_reserved": False,
            "ip_version": None,
            "geolocation": {},
            "asn_info": {},
            "reverse_dns": None,
            "reputation": {},
            "error": None
        }
        
        try:
            # Validate IP address
            ip_obj = ipaddress.ip_address(ip_address)
            result["ip_version"] = ip_obj.version
            result["is_private"] = ip_obj.is_private
            result["is_reserved"] = ip_obj.is_reserved
            
            # Skip lookups for private/reserved IPs
            if result["is_private"] or result["is_reserved"]:
                result["geolocation"] = {"note": "Private/Reserved IP - no geolocation data"}
                result["asn_info"] = {"note": "Private/Reserved IP - no ASN data"}
                return result
            
            # Perform geolocation lookup
            result["geolocation"] = self._geoip_lookup(ip_address)
            
            # Perform ASN lookup
            result["asn_info"] = self._asn_lookup(ip_address)
            
            # Perform reverse DNS lookup
            result["reverse_dns"] = self._reverse_dns_lookup(ip_address)
            
            # Check IP reputation (basic)
            result["reputation"] = self._check_ip_reputation(ip_address)
        
        except ValueError:
            result["error"] = "Invalid IP address format"
        except Exception as e:
            result["error"] = str(e)
        
        return result
    
    def _geoip_lookup(self, ip_address):
        """Perform GeoIP lookup using local database"""
        result = {
            "country": None,
            "country_code": None,
            "region": None,
            "city": None,
            "postal_code": None,
            "latitude": None,
            "longitude": None,
            "timezone": None,
            "accuracy_radius": None,
            "source": "unknown"
        }
        
        # Try local GeoIP database first
        if self.geoip_reader:
            try:
                response = self.geoip_reader.city(ip_address)
                
                result.update({
                    "country": response.country.name,
                    "country_code": response.country.iso_code,
                    "region": response.subdivisions.most_specific.name,
                    "city": response.city.name,
                    "postal_code": response.postal.code,
                    "latitude": float(response.location.latitude) if response.location.latitude else None,
                    "longitude": float(response.location.longitude) if response.location.longitude else None,
                    "timezone": response.location.time_zone,
                    "accuracy_radius": response.location.accuracy_radius,
                    "source": "GeoLite2"
                })
                
                return result
            
            except geoip2.errors.AddressNotFoundError:
                result["error"] = "IP address not found in GeoIP database"
            except Exception as e:
                result["error"] = f"GeoIP lookup failed: {str(e)}"
        
        # Fallback to online API
        return self._online_geoip_lookup(ip_address)
    
    def _online_geoip_lookup(self, ip_address):
        """Fallback to online GeoIP service"""
        result = {
            "country": None,
            "country_code": None,
            "region": None,
            "city": None,
            "latitude": None,
            "longitude": None,
            "timezone": None,
            "source": "online_api"
        }
        
        try:
            # Try ip-api.com (free service)
            url = f"http://ip-api.com/json/{ip_address}"
            response = requests.get(url, timeout=self.timeout)
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get('status') == 'success':
                    result.update({
                        "country": data.get('country'),
                        "country_code": data.get('countryCode'),
                        "region": data.get('regionName'),
                        "city": data.get('city'),
                        "latitude": data.get('lat'),
                        "longitude": data.get('lon'),
                        "timezone": data.get('timezone'),
                        "source": "ip-api.com"
                    })
                else:
                    result["error"] = data.get('message', 'Unknown error')
            else:
                result["error"] = f"HTTP {response.status_code}"
        
        except Exception as e:
            result["error"] = f"Online GeoIP lookup failed: {str(e)}"
        
        return result
    
    def _asn_lookup(self, ip_address):
        """Perform ASN (Autonomous System Number) lookup"""
        result = {
            "asn": None,
            "organization": None,
            "network": None,
            "source": "unknown"
        }
        
        # Try local ASN database if available
        if self.geoip_reader:
            try:
                # Note: This requires GeoLite2-ASN database, not City database
                # For now, we'll use online lookup
                pass
            except:
                pass
        
        # Online ASN lookup
        try:
            # Try ipinfo.io API
            url = f"https://ipinfo.io/{ip_address}/json"
            response = requests.get(url, timeout=self.timeout)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'org' in data:
                    org_info = data['org']
                    # Parse ASN from org field (format: "AS12345 Organization Name")
                    if org_info.startswith('AS'):
                        parts = org_info.split(' ', 1)
                        result["asn"] = parts[0]
                        result["organization"] = parts[1] if len(parts) > 1 else None
                    else:
                        result["organization"] = org_info
                
                result["network"] = data.get('network')
                result["source"] = "ipinfo.io"
        
        except Exception as e:
            result["error"] = f"ASN lookup failed: {str(e)}"
        
        return result
    
    def _reverse_dns_lookup(self, ip_address):
        """Perform reverse DNS lookup"""
        try:
            hostname = socket.gethostbyaddr(ip_address)[0]
            return hostname
        except socket.herror:
            return None
        except Exception:
            return None
    
    def _check_ip_reputation(self, ip_address):
        """Basic IP reputation check"""
        result = {
            "is_malicious": False,
            "reputation_score": None,
            "threat_types": [],
            "last_seen": None,
            "source": "basic_check"
        }
        
        # Basic checks
        try:
            # Check if IP is in common blacklists (simplified)
            # In a real implementation, you would check against threat intelligence feeds
            
            # Check for common malicious patterns
            ip_obj = ipaddress.ip_address(ip_address)
            
            # Check for Tor exit nodes (simplified check)
            if self._is_tor_exit_node(ip_address):
                result["threat_types"].append("tor_exit_node")
            
            # Check for known bad ranges (example)
            if self._is_suspicious_range(ip_obj):
                result["threat_types"].append("suspicious_range")
            
            if result["threat_types"]:
                result["is_malicious"] = True
                result["reputation_score"] = 0.3  # Low score for suspicious IPs
            else:
                result["reputation_score"] = 0.8  # Neutral score
        
        except Exception as e:
            result["error"] = f"Reputation check failed: {str(e)}"
        
        return result
    
    def _is_tor_exit_node(self, ip_address):
        """Check if IP is a known Tor exit node (simplified)"""
        # In a real implementation, you would check against Tor exit node lists
        # This is a placeholder
        return False
    
    def _is_suspicious_range(self, ip_obj):
        """Check if IP is in a suspicious range (simplified)"""
        # Example suspicious ranges (this is just an example)
        suspicious_ranges = [
            ipaddress.ip_network('************/24'),  # Example range
        ]
        
        for network in suspicious_ranges:
            if ip_obj in network:
                return True
        
        return False
    
    def bulk_lookup(self, ip_addresses):
        """Perform bulk IP lookups"""
        results = []
        
        for ip in ip_addresses:
            result = self.lookup_ip(ip)
            results.append(result)
        
        return {
            "timestamp": datetime.datetime.now().isoformat(),
            "total_ips": len(ip_addresses),
            "results": results,
            "summary": self._generate_bulk_summary(results)
        }
    
    def _generate_bulk_summary(self, results):
        """Generate summary for bulk lookup results"""
        summary = {
            "countries": {},
            "asns": {},
            "private_ips": 0,
            "malicious_ips": 0,
            "total_processed": len(results)
        }
        
        for result in results:
            # Count countries
            if result.get("geolocation", {}).get("country"):
                country = result["geolocation"]["country"]
                summary["countries"][country] = summary["countries"].get(country, 0) + 1
            
            # Count ASNs
            if result.get("asn_info", {}).get("asn"):
                asn = result["asn_info"]["asn"]
                summary["asns"][asn] = summary["asns"].get(asn, 0) + 1
            
            # Count private IPs
            if result.get("is_private"):
                summary["private_ips"] += 1
            
            # Count malicious IPs
            if result.get("reputation", {}).get("is_malicious"):
                summary["malicious_ips"] += 1
        
        return summary
    
    def get_ip_from_domain(self, domain):
        """Resolve domain to IP addresses"""
        result = {
            "domain": domain,
            "timestamp": datetime.datetime.now().isoformat(),
            "ipv4_addresses": [],
            "ipv6_addresses": [],
            "error": None
        }
        
        try:
            # Get IPv4 addresses
            try:
                ipv4_results = socket.getaddrinfo(domain, None, socket.AF_INET)
                result["ipv4_addresses"] = list(set([addr[4][0] for addr in ipv4_results]))
            except socket.gaierror:
                pass
            
            # Get IPv6 addresses
            try:
                ipv6_results = socket.getaddrinfo(domain, None, socket.AF_INET6)
                result["ipv6_addresses"] = list(set([addr[4][0] for addr in ipv6_results]))
            except socket.gaierror:
                pass
            
            if not result["ipv4_addresses"] and not result["ipv6_addresses"]:
                result["error"] = "No IP addresses found for domain"
        
        except Exception as e:
            result["error"] = str(e)
        
        return result
    
    def comprehensive_domain_lookup(self, domain):
        """Perform comprehensive lookup for a domain"""
        result = {
            "domain": domain,
            "timestamp": datetime.datetime.now().isoformat(),
            "dns_resolution": self.get_ip_from_domain(domain),
            "ip_lookups": []
        }
        
        # Perform GeoIP lookup for each resolved IP
        all_ips = result["dns_resolution"]["ipv4_addresses"] + result["dns_resolution"]["ipv6_addresses"]
        
        for ip in all_ips:
            ip_result = self.lookup_ip(ip)
            result["ip_lookups"].append(ip_result)
        
        return result

def main():
    """Test the GeoIP lookup"""
    geoip = GeoIPLookup()
    
    # Test IP lookup
    test_ip = "*******"
    print(f"Testing GeoIP lookup for {test_ip}...")
    
    result = geoip.lookup_ip(test_ip)
    print(json.dumps(result, indent=2))
    
    # Test domain lookup
    test_domain = "google.com"
    print(f"\nTesting domain lookup for {test_domain}...")
    
    domain_result = geoip.comprehensive_domain_lookup(test_domain)
    print(json.dumps(domain_result, indent=2))

if __name__ == "__main__":
    main()
