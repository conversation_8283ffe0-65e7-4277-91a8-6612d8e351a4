#!/usr/bin/env python3
"""
Web Server for Cybersecurity Scanner
Serves the static website and handles API calls to the orchestrator
"""

import json
import os
import sys
import threading
import time
from http.server import HTTPServer, SimpleHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import subprocess
import tempfile

# Add python_modules to path
sys.path.append('python_modules')
from cybersecurity_orchestrator import CybersecurityOrchestrator

class ScannerHTTPRequestHandler(SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        # Initialize orchestrator
        self.orchestrator = CybersecurityOrchestrator()
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """Handle GET requests"""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == '/':
            # Serve index.html
            self.path = '/web_interface/index.html'
        elif parsed_path.path.startswith('/api/'):
            # Handle API requests
            self.handle_api_request(parsed_path)
            return
        elif not parsed_path.path.startswith('/web_interface/'):
            # Redirect to web_interface directory
            self.path = '/web_interface' + parsed_path.path
        
        # Serve static files
        super().do_GET()
    
    def do_POST(self):
        """Handle POST requests"""
        parsed_path = urlparse(self.path)
        
        if parsed_path.path.startswith('/api/'):
            self.handle_api_request(parsed_path)
        else:
            self.send_error(404, "Not Found")
    
    def handle_api_request(self, parsed_path):
        """Handle API requests"""
        try:
            if parsed_path.path == '/api/scan':
                self.handle_scan_request()
            elif parsed_path.path == '/api/status':
                self.handle_status_request()
            elif parsed_path.path == '/api/results':
                self.handle_results_request()
            else:
                self.send_error(404, "API endpoint not found")
        except Exception as e:
            self.send_json_response({'error': str(e)}, 500)
    
    def handle_scan_request(self):
        """Handle scan requests"""
        if self.command == 'POST':
            # Get request data
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            
            try:
                request_data = json.loads(post_data.decode('utf-8'))
            except json.JSONDecodeError:
                self.send_json_response({'error': 'Invalid JSON'}, 400)
                return
            
            target = request_data.get('target')
            scan_type = request_data.get('scan_type', 'comprehensive')
            scan_options = request_data.get('scan_options', {})
            
            if not target:
                self.send_json_response({'error': 'Target is required'}, 400)
                return
            
            # Start scan in background thread
            scan_id = f"scan_{int(time.time())}"
            thread = threading.Thread(
                target=self.run_scan_background,
                args=(scan_id, target, scan_type, scan_options)
            )
            thread.daemon = True
            thread.start()
            
            self.send_json_response({
                'scan_id': scan_id,
                'status': 'started',
                'message': 'Scan started successfully'
            })
        else:
            self.send_error(405, "Method Not Allowed")
    
    def run_scan_background(self, scan_id, target, scan_type, scan_options):
        """Run scan in background thread"""
        try:
            # Store scan status
            ScannerHTTPRequestHandler.active_scans[scan_id] = {
                'status': 'running',
                'progress': 0,
                'current_step': 'Initializing...',
                'start_time': time.time()
            }
            
            # Perform the actual scan
            if scan_type == 'quick':
                results = self.orchestrator.quick_scan(target)
            elif scan_type == 'deep':
                results = self.orchestrator.deep_scan(target)
            else:
                results = self.orchestrator.comprehensive_scan(target, scan_options)
            
            # Store results
            ScannerHTTPRequestHandler.scan_results[scan_id] = results
            ScannerHTTPRequestHandler.active_scans[scan_id] = {
                'status': 'completed',
                'progress': 100,
                'current_step': 'Completed',
                'end_time': time.time()
            }
            
        except Exception as e:
            ScannerHTTPRequestHandler.active_scans[scan_id] = {
                'status': 'failed',
                'progress': 0,
                'current_step': f'Failed: {str(e)}',
                'error': str(e)
            }
    
    def handle_status_request(self):
        """Handle scan status requests"""
        if self.command == 'GET':
            query_params = parse_qs(urlparse(self.path).query)
            scan_id = query_params.get('scan_id', [None])[0]
            
            if not scan_id:
                self.send_json_response({'error': 'scan_id is required'}, 400)
                return
            
            if scan_id in ScannerHTTPRequestHandler.active_scans:
                status = ScannerHTTPRequestHandler.active_scans[scan_id]
                self.send_json_response(status)
            else:
                self.send_json_response({'error': 'Scan not found'}, 404)
        else:
            self.send_error(405, "Method Not Allowed")
    
    def handle_results_request(self):
        """Handle scan results requests"""
        if self.command == 'GET':
            query_params = parse_qs(urlparse(self.path).query)
            scan_id = query_params.get('scan_id', [None])[0]
            
            if not scan_id:
                self.send_json_response({'error': 'scan_id is required'}, 400)
                return
            
            if scan_id in ScannerHTTPRequestHandler.scan_results:
                results = ScannerHTTPRequestHandler.scan_results[scan_id]
                self.send_json_response(results)
            else:
                self.send_json_response({'error': 'Results not found'}, 404)
        else:
            self.send_error(405, "Method Not Allowed")
    
    def send_json_response(self, data, status_code=200):
        """Send JSON response"""
        response_data = json.dumps(data, indent=2)
        
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Content-Length', str(len(response_data)))
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        
        self.wfile.write(response_data.encode('utf-8'))
    
    def do_OPTIONS(self):
        """Handle OPTIONS requests for CORS"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def log_message(self, format, *args):
        """Override to customize logging"""
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

# Class variables to store scan data
ScannerHTTPRequestHandler.active_scans = {}
ScannerHTTPRequestHandler.scan_results = {}

class CybersecurityWebServer:
    def __init__(self, host='localhost', port=8080):
        self.host = host
        self.port = port
        self.server = None
    
    def start(self):
        """Start the web server"""
        try:
            # Change to the project directory to serve files correctly
            os.chdir(os.path.dirname(os.path.abspath(__file__)))
            
            self.server = HTTPServer((self.host, self.port), ScannerHTTPRequestHandler)
            
            print(f"Starting Cybersecurity Scanner Web Server...")
            print(f"Server running at http://{self.host}:{self.port}/")
            print(f"Open your browser and navigate to the URL above")
            print(f"Press Ctrl+C to stop the server")
            
            self.server.serve_forever()
            
        except KeyboardInterrupt:
            print("\nShutting down server...")
            self.stop()
        except Exception as e:
            print(f"Server error: {e}")
    
    def stop(self):
        """Stop the web server"""
        if self.server:
            self.server.shutdown()
            self.server.server_close()
            print("Server stopped.")

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Cybersecurity Scanner Web Server")
    parser.add_argument('--host', default='localhost', help='Host to bind to (default: localhost)')
    parser.add_argument('--port', type=int, default=8080, help='Port to bind to (default: 8080)')
    parser.add_argument('--open-browser', action='store_true', help='Open browser automatically')
    
    args = parser.parse_args()
    
    # Create and start server
    server = CybersecurityWebServer(args.host, args.port)
    
    # Open browser if requested
    if args.open_browser:
        import webbrowser
        url = f"http://{args.host}:{args.port}/"
        print(f"Opening browser to {url}")
        webbrowser.open(url)
    
    server.start()

if __name__ == "__main__":
    main()
