# Developer Guide

This guide provides technical information for developers who want to understand, modify, or extend the Cybersecurity Scanner.

## Architecture Overview

### System Design

The Cybersecurity Scanner follows a modular, hybrid architecture:

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Web Interface │    │  REST API Server │    │ Scan Orchestrator│
│   (JavaScript)  │◄──►│   (Python)       │◄──►│   (Python)      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
                       ┌─────────────────────────────────┼─────────────────────────────────┐
                       │                                 ▼                                 │
                       │                    ┌─────────────────────┐                       │
                       │                    │   Security Modules  │                       │
                       │                    │     (Python)        │                       │
                       │                    └─────────────────────┘                       │
                       │                                 │                                 │
        ┌──────────────▼──────────────┐    ┌─────────────▼─────────────┐    ┌─────────────▼─────────────┐
        │     DNS Health Checker      │    │     Port Scanner         │    │    SSL/TLS Checker       │
        │   (C++ with Python fallback)│    │  (nmap + Python sockets) │    │ (OpenSSL + Python crypto)│
        └─────────────────────────────┘    └─────────────────────────────┘    └─────────────────────────────┘
        
        ┌─────────────────────────────┐    ┌─────────────────────────────┐    ┌─────────────────────────────┐
        │   HTTP Security Analyzer   │    │   Email Security Checker   │    │     GeoIP Lookup           │
        │       (Python)             │    │       (Python)             │    │       (Python)             │
        └─────────────────────────────┘    └─────────────────────────────┘    └─────────────────────────────┘
```

### Core Components

1. **Scan Orchestrator** (`cybersecurity_orchestrator.py`)
   - Main coordination logic
   - Manages scan lifecycle
   - Handles result aggregation
   - Provides fallback mechanisms

2. **Web Server** (`web_server.py`)
   - HTTP server for web interface
   - REST API endpoints
   - Static file serving
   - Real-time progress tracking

3. **Security Modules** (`python_modules/`)
   - Modular security checking components
   - Independent, reusable modules
   - Consistent interface design

4. **Web Interface** (`web_interface/`)
   - Modern JavaScript frontend
   - Real-time progress updates
   - Interactive result visualization

## Code Structure

### Directory Layout
```
cybersecurity-scanner/
├── cybersecurity_orchestrator.py    # Main orchestrator
├── web_server.py                    # HTTP server
├── setup_windows.py                 # Windows setup script
├── requirements.txt                 # Python dependencies
├── README.md                        # Main documentation
├── python_modules/                  # Security modules
│   ├── __init__.py
│   ├── port_scanner.py             # Port scanning
│   ├── ssl_checker.py              # SSL/TLS analysis
│   ├── http_security.py            # HTTP security headers
│   ├── email_security.py           # Email security validation
│   └── geoip_lookup.py             # GeoIP and ASN lookup
├── web_interface/                   # Frontend
│   ├── index.html                  # Main web page
│   ├── script.js                   # JavaScript logic
│   └── styles.css                  # CSS styling
├── results/                         # Scan results (JSON)
├── logs/                           # Log files
└── docs/                           # Documentation
    ├── API_REFERENCE.md
    ├── INSTALLATION.md
    ├── TROUBLESHOOTING.md
    └── DEVELOPER_GUIDE.md
```

### Module Interface Design

All security modules follow a consistent interface pattern:

```python
def check_security_aspect(target, options=None):
    """
    Standard interface for security modules.
    
    Args:
        target (str): Domain name or IP address
        options (dict): Module-specific options
        
    Returns:
        dict: Standardized result structure
    """
    return {
        'status': 'success|error|warning',
        'timestamp': '2025-07-01T12:00:00.000000',
        'target': target,
        'results': {
            # Module-specific results
        },
        'issues': [
            {
                'severity': 'low|medium|high|critical',
                'type': 'vulnerability|misconfiguration|warning',
                'description': 'Human-readable description',
                'recommendation': 'How to fix this issue'
            }
        ],
        'metadata': {
            'scan_duration': 1.23,
            'method': 'nmap|python|openssl',
            'version': '1.0.0'
        }
    }
```

## Adding New Security Modules

### Step 1: Create Module File

Create a new file in `python_modules/`:

```python
# python_modules/new_security_check.py
import time
from datetime import datetime

def check_new_security_aspect(target, options=None):
    """
    New security check implementation.
    """
    start_time = time.time()
    
    try:
        # Implement your security check logic here
        results = perform_security_check(target, options)
        
        return {
            'status': 'success',
            'timestamp': datetime.now().isoformat(),
            'target': target,
            'results': results,
            'issues': [],
            'metadata': {
                'scan_duration': time.time() - start_time,
                'method': 'custom',
                'version': '1.0.0'
            }
        }
    except Exception as e:
        return {
            'status': 'error',
            'timestamp': datetime.now().isoformat(),
            'target': target,
            'error': str(e),
            'metadata': {
                'scan_duration': time.time() - start_time
            }
        }

def perform_security_check(target, options):
    """
    Implement your specific security check logic.
    """
    # Your implementation here
    pass
```

### Step 2: Integrate with Orchestrator

Modify `cybersecurity_orchestrator.py`:

```python
# Add import
from python_modules.new_security_check import check_new_security_aspect

# Add to scan options
def get_scan_options(scan_type):
    options = {
        # ... existing options ...
        'new_security_check': scan_type in ['comprehensive', 'deep']
    }
    return options

# Add to main scanning logic
def perform_scan(target, scan_options):
    results = {}
    
    # ... existing checks ...
    
    if scan_options.get('new_security_check'):
        print("Performing new security check...")
        results['new_security'] = check_new_security_aspect(target)
    
    return results
```

### Step 3: Update Web Interface

Add to `web_interface/script.js`:

```javascript
function displayResults(results) {
    // ... existing display logic ...
    
    if (results.new_security) {
        displayNewSecurityResults(results.new_security);
    }
}

function displayNewSecurityResults(newSecurityData) {
    const section = document.createElement('div');
    section.className = 'result-section';
    section.innerHTML = `
        <h3>New Security Check</h3>
        <div class="result-content">
            <!-- Display your results here -->
        </div>
    `;
    document.getElementById('results').appendChild(section);
}
```

## Extending Existing Modules

### Port Scanner Extension

To add new port scanning techniques:

```python
# In python_modules/port_scanner.py

def scan_ports_advanced(target, ports, method='syn'):
    """
    Advanced port scanning with different techniques.
    """
    if method == 'syn':
        return syn_scan(target, ports)
    elif method == 'connect':
        return connect_scan(target, ports)
    elif method == 'udp':
        return udp_scan(target, ports)
    else:
        raise ValueError(f"Unknown scan method: {method}")

def syn_scan(target, ports):
    """
    SYN scan implementation (requires root privileges).
    """
    # Implement SYN scanning using raw sockets
    pass
```

### SSL Checker Enhancement

To add new SSL/TLS checks:

```python
# In python_modules/ssl_checker.py

def check_ssl_advanced(target, port=443):
    """
    Advanced SSL/TLS security analysis.
    """
    results = check_ssl_basic(target, port)
    
    # Add advanced checks
    results['cipher_analysis'] = analyze_cipher_suites(target, port)
    results['vulnerability_scan'] = scan_ssl_vulnerabilities(target, port)
    results['certificate_transparency'] = check_ct_logs(target)
    
    return results

def scan_ssl_vulnerabilities(target, port):
    """
    Check for known SSL/TLS vulnerabilities.
    """
    vulnerabilities = []
    
    # Check for Heartbleed
    if check_heartbleed(target, port):
        vulnerabilities.append({
            'name': 'Heartbleed',
            'severity': 'critical',
            'cve': 'CVE-2014-0160'
        })
    
    # Check for POODLE
    if check_poodle(target, port):
        vulnerabilities.append({
            'name': 'POODLE',
            'severity': 'high',
            'cve': 'CVE-2014-3566'
        })
    
    return vulnerabilities
```

## API Development

### Adding New Endpoints

To add new API endpoints, modify `web_server.py`:

```python
class ScanHandler(http.server.BaseHTTPRequestHandler):
    def do_POST(self):
        if self.path == '/api/scan':
            self.handle_start_scan()
        elif self.path == '/api/custom-scan':
            self.handle_custom_scan()
        # ... existing endpoints ...
    
    def handle_custom_scan(self):
        """
        Handle custom scan requests.
        """
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            # Validate custom scan parameters
            if not self.validate_custom_scan(data):
                self.send_error_response(400, "Invalid custom scan parameters")
                return
            
            # Start custom scan
            scan_id = self.start_custom_scan(data)
            
            response = {
                'status': 'success',
                'scan_id': scan_id,
                'message': 'Custom scan initiated'
            }
            
            self.send_json_response(response)
            
        except Exception as e:
            self.send_error_response(500, f"Custom scan failed: {str(e)}")
```

### WebSocket Support

For real-time updates, add WebSocket support:

```python
import websockets
import asyncio

class WebSocketHandler:
    def __init__(self):
        self.clients = set()
    
    async def register(self, websocket):
        self.clients.add(websocket)
    
    async def unregister(self, websocket):
        self.clients.remove(websocket)
    
    async def broadcast_progress(self, scan_id, progress):
        if self.clients:
            message = json.dumps({
                'type': 'progress',
                'scan_id': scan_id,
                'progress': progress
            })
            await asyncio.gather(
                *[client.send(message) for client in self.clients],
                return_exceptions=True
            )
```

## Testing Framework

### Unit Tests

Create test files in `tests/` directory:

```python
# tests/test_port_scanner.py
import unittest
from python_modules.port_scanner import scan_ports

class TestPortScanner(unittest.TestCase):
    def test_scan_open_port(self):
        """Test scanning a known open port."""
        result = scan_ports('google.com', [80])
        self.assertIn(80, result['open_ports'])
    
    def test_scan_closed_port(self):
        """Test scanning a likely closed port."""
        result = scan_ports('google.com', [12345])
        self.assertNotIn(12345, result['open_ports'])
    
    def test_invalid_target(self):
        """Test handling of invalid targets."""
        with self.assertRaises(Exception):
            scan_ports('invalid.domain.that.does.not.exist', [80])

if __name__ == '__main__':
    unittest.main()
```

### Integration Tests

```python
# tests/test_integration.py
import unittest
import requests
import time
from cybersecurity_orchestrator import perform_scan

class TestIntegration(unittest.TestCase):
    def test_full_scan_workflow(self):
        """Test complete scan workflow."""
        target = 'google.com'
        scan_options = {
            'dns_check': True,
            'port_scan': True,
            'ssl_check': True
        }
        
        results = perform_scan(target, scan_options)
        
        self.assertIn('dns_health', results)
        self.assertIn('port_scan', results)
        self.assertIn('ssl_tls', results)
        
        # Verify result structure
        self.assertEqual(results['dns_health']['status'], 'success')
        self.assertGreater(len(results['port_scan']['open_ports']), 0)
```

### Performance Tests

```python
# tests/test_performance.py
import time
import unittest
from cybersecurity_orchestrator import perform_scan

class TestPerformance(unittest.TestCase):
    def test_quick_scan_performance(self):
        """Ensure quick scans complete within reasonable time."""
        start_time = time.time()
        
        results = perform_scan('google.com', {
            'dns_check': True,
            'port_scan': True,
            'ssl_check': True
        })
        
        duration = time.time() - start_time
        self.assertLess(duration, 120)  # Should complete within 2 minutes
```

## Configuration Management

### Environment-Based Configuration

```python
# config.py
import os
from typing import Dict, Any

class Config:
    def __init__(self):
        self.settings = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        return {
            'server': {
                'host': os.getenv('SERVER_HOST', 'localhost'),
                'port': int(os.getenv('SERVER_PORT', 8080)),
                'debug': os.getenv('DEBUG', 'false').lower() == 'true'
            },
            'scanning': {
                'timeout': int(os.getenv('SCAN_TIMEOUT', 30)),
                'max_threads': int(os.getenv('MAX_THREADS', 50)),
                'results_dir': os.getenv('RESULTS_DIR', 'results')
            },
            'external_tools': {
                'nmap_path': os.getenv('NMAP_PATH', 'nmap'),
                'openssl_path': os.getenv('OPENSSL_PATH', 'openssl')
            }
        }
    
    def get(self, key: str, default=None):
        keys = key.split('.')
        value = self.settings
        for k in keys:
            value = value.get(k, {})
        return value if value != {} else default

# Usage
config = Config()
timeout = config.get('scanning.timeout', 30)
```

## Logging and Monitoring

### Structured Logging

```python
# logging_config.py
import logging
import json
from datetime import datetime

class JSONFormatter(logging.Formatter):
    def format(self, record):
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': record.levelname,
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno,
            'message': record.getMessage()
        }
        
        if hasattr(record, 'scan_id'):
            log_entry['scan_id'] = record.scan_id
        
        if hasattr(record, 'target'):
            log_entry['target'] = record.target
        
        return json.dumps(log_entry)

def setup_logging():
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    
    handler = logging.FileHandler('scanner.log')
    handler.setFormatter(JSONFormatter())
    logger.addHandler(handler)
    
    return logger
```

### Performance Monitoring

```python
# monitoring.py
import time
import psutil
from functools import wraps

def monitor_performance(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss
        
        result = func(*args, **kwargs)
        
        end_time = time.time()
        end_memory = psutil.Process().memory_info().rss
        
        performance_data = {
            'function': func.__name__,
            'duration': end_time - start_time,
            'memory_delta': end_memory - start_memory,
            'timestamp': time.time()
        }
        
        # Log performance data
        logger.info("Performance data", extra=performance_data)
        
        return result
    return wrapper

# Usage
@monitor_performance
def scan_ports(target, ports):
    # Implementation
    pass
```

## Security Considerations

### Input Validation

```python
import re
import ipaddress

def validate_target(target):
    """
    Validate scan target to prevent injection attacks.
    """
    # Check for valid domain name
    domain_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
    
    if re.match(domain_pattern, target):
        return True
    
    # Check for valid IP address
    try:
        ipaddress.ip_address(target)
        return True
    except ValueError:
        pass
    
    return False

def sanitize_input(user_input):
    """
    Sanitize user input to prevent command injection.
    """
    # Remove dangerous characters
    dangerous_chars = ['&', '|', ';', '$', '`', '(', ')', '<', '>', '"', "'"]
    for char in dangerous_chars:
        user_input = user_input.replace(char, '')
    
    return user_input.strip()
```

### Rate Limiting

```python
from collections import defaultdict
import time

class RateLimiter:
    def __init__(self, max_requests=10, time_window=60):
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = defaultdict(list)
    
    def is_allowed(self, client_id):
        now = time.time()
        client_requests = self.requests[client_id]
        
        # Remove old requests
        client_requests[:] = [req_time for req_time in client_requests 
                             if now - req_time < self.time_window]
        
        if len(client_requests) >= self.max_requests:
            return False
        
        client_requests.append(now)
        return True
```

## Deployment Considerations

### Docker Deployment

```dockerfile
# Dockerfile
FROM python:3.9-slim

# Install system dependencies
RUN apt-get update && apt-get install -y \
    nmap \
    openssl \
    dnsutils \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create non-root user
RUN useradd -m -u 1000 scanner && chown -R scanner:scanner /app
USER scanner

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Start application
CMD ["python", "web_server.py"]
```

### Production Configuration

```python
# production_config.py
import os

class ProductionConfig:
    # Security settings
    SECRET_KEY = os.environ.get('SECRET_KEY')
    SSL_CERT_PATH = os.environ.get('SSL_CERT_PATH')
    SSL_KEY_PATH = os.environ.get('SSL_KEY_PATH')
    
    # Database settings (if using database)
    DATABASE_URL = os.environ.get('DATABASE_URL')
    
    # Logging
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FILE = os.environ.get('LOG_FILE', '/var/log/scanner.log')
    
    # Performance
    MAX_WORKERS = int(os.environ.get('MAX_WORKERS', 4))
    WORKER_TIMEOUT = int(os.environ.get('WORKER_TIMEOUT', 300))
    
    # Rate limiting
    RATE_LIMIT_ENABLED = os.environ.get('RATE_LIMIT_ENABLED', 'true').lower() == 'true'
    RATE_LIMIT_REQUESTS = int(os.environ.get('RATE_LIMIT_REQUESTS', 100))
    RATE_LIMIT_WINDOW = int(os.environ.get('RATE_LIMIT_WINDOW', 3600))
```

This developer guide provides the foundation for understanding and extending the Cybersecurity Scanner. For specific implementation questions or advanced customization needs, refer to the individual module documentation and code comments.
