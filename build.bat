@echo off
echo Building Cybersecurity Scanner...

REM Create build directory
if not exist build mkdir build
cd build

REM Try CMake build
cmake .. -DCMAKE_PREFIX_PATH=include
if %ERRORLEVEL% EQU 0 (
    cmake --build .
    if %ERRORLEVEL% EQU 0 (
        echo Build successful!
        goto :end
    )
)

REM Fallback to manual compilation
echo CMake failed, trying manual compilation...
cd ..
g++ -std=c++17 -Iinclude -Isrc src/*.cpp -o cybersecurity_scanner.exe
if %ERRORLEVEL% EQU 0 (
    echo Manual build successful!
) else (
    echo Build failed!
)

:end
pause
