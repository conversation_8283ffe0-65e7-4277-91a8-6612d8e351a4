#!/usr/bin/env python3
"""
Auto-setup script for Cybersecurity Scanner
Installs all required dependencies automatically
"""

import subprocess
import sys
import os
import platform
import urllib.request
import json
import shutil
from pathlib import Path

def run_command(cmd, shell=True, check=True):
    """Run a command and return the result"""
    try:
        result = subprocess.run(cmd, shell=shell, check=check, 
                              capture_output=True, text=True)
        return result.returncode == 0, result.stdout, result.stderr
    except subprocess.CalledProcessError as e:
        return False, e.stdout, e.stderr

def install_python_packages():
    """Install Python packages from requirements.txt"""
    print("Installing Python packages...")
    
    # Upgrade pip first
    success, stdout, stderr = run_command([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], shell=False)
    if not success:
        print(f"Warning: Could not upgrade pip: {stderr}")
    
    # Install requirements
    if os.path.exists("requirements.txt"):
        success, stdout, stderr = run_command([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], shell=False)
        if success:
            print("✓ Python packages installed successfully")
        else:
            print(f"✗ Failed to install Python packages: {stderr}")
            return False
    else:
        print("✗ requirements.txt not found")
        return False
    
    return True

def download_nlohmann_json():
    """Download nlohmann/json header if not found"""
    print("Checking for nlohmann/json...")
    
    # Check if already exists
    json_paths = [
        "/usr/include/nlohmann/json.hpp",
        "/usr/local/include/nlohmann/json.hpp",
        "include/nlohmann/json.hpp",
        "third_party/nlohmann/json.hpp"
    ]
    
    for path in json_paths:
        if os.path.exists(path):
            print(f"✓ Found nlohmann/json at {path}")
            return True
    
    # Download it
    print("Downloading nlohmann/json...")
    os.makedirs("include/nlohmann", exist_ok=True)
    
    try:
        url = "https://github.com/nlohmann/json/releases/download/v3.11.2/json.hpp"
        urllib.request.urlretrieve(url, "include/nlohmann/json.hpp")
        print("✓ nlohmann/json downloaded successfully")
        return True
    except Exception as e:
        print(f"✗ Failed to download nlohmann/json: {e}")
        return False

def install_nmap():
    """Install nmap if not available"""
    print("Checking for nmap...")
    
    # Check if nmap is already installed
    success, _, _ = run_command("nmap --version")
    if success:
        print("✓ nmap is already installed")
        return True
    
    system = platform.system().lower()
    
    if system == "linux":
        # Try different package managers
        managers = [
            ("apt-get", ["sudo", "apt-get", "update", "&&", "sudo", "apt-get", "install", "-y", "nmap"]),
            ("yum", ["sudo", "yum", "install", "-y", "nmap"]),
            ("dnf", ["sudo", "dnf", "install", "-y", "nmap"]),
            ("pacman", ["sudo", "pacman", "-S", "--noconfirm", "nmap"])
        ]
        
        for manager, cmd in managers:
            if shutil.which(manager.split()[0] if manager != "apt-get" else "apt"):
                print(f"Installing nmap using {manager}...")
                success, _, stderr = run_command(" ".join(cmd))
                if success:
                    print("✓ nmap installed successfully")
                    return True
                else:
                    print(f"Failed with {manager}: {stderr}")
    
    elif system == "darwin":  # macOS
        if shutil.which("brew"):
            print("Installing nmap using Homebrew...")
            success, _, stderr = run_command("brew install nmap")
            if success:
                print("✓ nmap installed successfully")
                return True
        
        if shutil.which("port"):
            print("Installing nmap using MacPorts...")
            success, _, stderr = run_command("sudo port install nmap")
            if success:
                print("✓ nmap installed successfully")
                return True
    
    elif system == "windows":
        print("Please install nmap manually from https://nmap.org/download.html")
        return False
    
    print("✗ Could not install nmap automatically")
    return False

def install_cpp_dependencies():
    """Install C++ dependencies"""
    print("Installing C++ dependencies...")
    
    system = platform.system().lower()
    
    if system == "linux":
        # Install ldns
        managers = [
            ("apt-get", ["sudo", "apt-get", "install", "-y", "libldns-dev"]),
            ("yum", ["sudo", "yum", "install", "-y", "ldns-devel"]),
            ("dnf", ["sudo", "dnf", "install", "-y", "ldns-devel"]),
            ("pacman", ["sudo", "pacman", "-S", "--noconfirm", "ldns"])
        ]
        
        for manager, cmd in managers:
            if shutil.which(manager.split()[0] if manager != "apt-get" else "apt"):
                print(f"Installing ldns using {manager}...")
                success, _, stderr = run_command(" ".join(cmd))
                if success:
                    print("✓ ldns installed successfully")
                    break
                else:
                    print(f"Failed with {manager}: {stderr}")
    
    elif system == "darwin":  # macOS
        if shutil.which("brew"):
            print("Installing ldns using Homebrew...")
            run_command("brew install ldns")
    
    elif system == "windows":
        print("For Windows, please install dependencies manually or use vcpkg")
    
    return True

def create_build_script():
    """Create a build script for the project"""
    print("Creating build script...")
    
    system = platform.system().lower()
    
    if system == "windows":
        script_content = """@echo off
echo Building Cybersecurity Scanner...

REM Create build directory
if not exist build mkdir build
cd build

REM Try CMake build
cmake .. -DCMAKE_PREFIX_PATH=include
if %ERRORLEVEL% EQU 0 (
    cmake --build .
    if %ERRORLEVEL% EQU 0 (
        echo Build successful!
        goto :end
    )
)

REM Fallback to manual compilation
echo CMake failed, trying manual compilation...
cd ..
g++ -std=c++17 -Iinclude -Isrc src/*.cpp -o cybersecurity_scanner.exe
if %ERRORLEVEL% EQU 0 (
    echo Manual build successful!
) else (
    echo Build failed!
)

:end
pause
"""
        with open("build.bat", "w") as f:
            f.write(script_content)
        print("✓ Created build.bat")
    
    else:  # Linux/macOS
        script_content = """#!/bin/bash
echo "Building Cybersecurity Scanner..."

# Create build directory
mkdir -p build
cd build

# Try CMake build
cmake .. -DCMAKE_PREFIX_PATH=../include
if [ $? -eq 0 ]; then
    make
    if [ $? -eq 0 ]; then
        echo "Build successful!"
        exit 0
    fi
fi

# Fallback to manual compilation
echo "CMake failed, trying manual compilation..."
cd ..
g++ -std=c++17 -Iinclude -Isrc src/*.cpp -o cybersecurity_scanner
if [ $? -eq 0 ]; then
    echo "Manual build successful!"
else
    echo "Build failed!"
    exit 1
fi
"""
        with open("build.sh", "w") as f:
            f.write(script_content)
        os.chmod("build.sh", 0o755)
        print("✓ Created build.sh")
    
    return True

def main():
    """Main setup function"""
    print("=== Cybersecurity Scanner Auto-Setup ===")
    print(f"Platform: {platform.system()} {platform.release()}")
    print(f"Python: {sys.version}")
    print()
    
    success = True
    
    # Install Python packages
    if not install_python_packages():
        success = False
    
    # Download nlohmann/json
    if not download_nlohmann_json():
        success = False
    
    # Install nmap
    if not install_nmap():
        print("Warning: nmap installation failed, port scanning may not work")
    
    # Install C++ dependencies
    if not install_cpp_dependencies():
        print("Warning: C++ dependencies installation failed")
    
    # Create build script
    if not create_build_script():
        success = False
    
    print()
    if success:
        print("✓ Setup completed successfully!")
        print("Run the build script to compile the project:")
        if platform.system().lower() == "windows":
            print("  build.bat")
        else:
            print("  ./build.sh")
    else:
        print("✗ Setup completed with warnings/errors")
        print("Please check the output above and install missing dependencies manually")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
