# Cybersecurity Scanner

A comprehensive cybersecurity scanning tool that performs DNS health checks, port scanning, SSL/TLS analysis, HTTP security assessment, email security validation, and GeoIP lookups for domains and IP addresses.

## Features

### Core Security Modules
- **DNS Health Checking**: A, AAAA, MX, NS record validation with response time analysis
- **Port Scanning**: TCP port discovery with service detection and banner grabbing
- **SSL/TLS Security**: Certificate validation, cipher analysis, HSTS verification
- **HTTP Security Headers**: Comprehensive security header analysis with scoring
- **Email Security**: SPF, DKIM, DMARC validation with SMTP testing
- **GeoIP & ASN Lookup**: IP geolocation, autonomous system identification, reputation checking

### Architecture
- **Hybrid C++/Python System**: C++ for performance-critical operations, Python for flexibility
- **Web Interface**: Modern JavaScript frontend with real-time progress tracking
- **RESTful API**: JSON-based API for programmatic access
- **Auto-Dependency Installation**: Automated setup scripts for all required libraries
- **Cross-Platform Support**: Windows-focused with fallback mechanisms

## Quick Start

### 1. Installation
```bash
# Run the automated setup script
python setup_windows.py

# Or install dependencies manually
pip install -r requirements.txt
```

### 2. Command Line Usage
```bash
# Quick scan (DNS, ports, SSL, GeoIP)
python cybersecurity_orchestrator.py example.com --scan-type quick

# Comprehensive scan (includes HTTP headers, email security)
python cybersecurity_orchestrator.py example.com --scan-type comprehensive

# Deep scan (extended port range, detailed analysis)
python cybersecurity_orchestrator.py example.com --scan-type deep

# Scan IP address
python cybersecurity_orchestrator.py ******* --scan-type quick
```

### 3. Web Interface
```bash
# Start the web server
python web_server.py

# Open browser to http://localhost:8080
```

## Scan Types

### Quick Scan
- DNS health check (A, AAAA, MX, NS records)
- Port scan (top 1000 ports)
- SSL/TLS analysis
- GeoIP lookup
- **Duration**: ~60-90 seconds

### Comprehensive Scan
- All Quick Scan features
- HTTP security headers analysis
- Email security (SPF, DKIM, DMARC)
- Directory listing detection
- Favicon fingerprinting
- **Duration**: ~2-3 minutes

### Deep Scan
- All Comprehensive Scan features
- Extended port range (1-65535)
- Detailed vulnerability analysis
- Advanced SSL cipher testing
- **Duration**: ~10-15 minutes

## API Reference

### Start Scan
```http
POST /api/scan
Content-Type: application/json

{
  "target": "example.com",
  "scan_type": "quick"
}
```

### Check Status
```http
GET /api/status?scan_id=scan_123456789
```

### Get Results
```http
GET /api/results?scan_id=scan_123456789
```

## Output Format

Results are saved as JSON files in the `results/` directory with detailed information:

```json
{
  "scan_id": "scan_123456789",
  "target": "example.com",
  "timestamp": "2025-07-01T12:00:00.000000",
  "scan_options": {
    "dns_check": true,
    "port_scan": true,
    "ssl_check": true,
    "http_security": false,
    "email_security": false,
    "geoip_lookup": true
  },
  "results": {
    "dns_health": { /* DNS analysis results */ },
    "port_scan": { /* Port scan results */ },
    "ssl_tls": { /* SSL/TLS analysis */ },
    "geoip": { /* GeoIP information */ }
  },
  "summary": {
    "total_checks": 5,
    "successful_checks": 5,
    "failed_checks": 0,
    "security_issues": [],
    "recommendations": [],
    "risk_score": 0,
    "open_ports": [80, 443],
    "ssl_issues": [],
    "email_issues": []
  }
}
```

## Security Analysis Features

### DNS Health Assessment
- Record resolution validation
- Response time measurement
- DNS server analysis
- IPv6 readiness check

### Port Security Analysis
- Service identification
- Banner grabbing
- Common vulnerability detection
- Protocol analysis

### SSL/TLS Security
- Certificate chain validation
- Cipher suite analysis
- Protocol version checking
- HSTS implementation verification

### HTTP Security Headers
- Content Security Policy (CSP)
- HTTP Strict Transport Security (HSTS)
- X-Frame-Options
- X-Content-Type-Options
- X-XSS-Protection
- Referrer Policy
- Permissions Policy

### Email Security Validation
- SPF record analysis
- DKIM signature validation
- DMARC policy assessment
- SMTP server testing
- Mail relay detection

## Dependencies

### Python Packages
- `requests`: HTTP client library
- `dnspython`: DNS resolution and analysis
- `cryptography`: SSL/TLS certificate handling
- `python-whois`: Domain information lookup
- `beautifulsoup4`: HTML parsing for web analysis

### Optional Tools
- `nmap`: Advanced port scanning (falls back to Python socket scanning)
- `openssl`: SSL/TLS analysis (falls back to Python cryptography)

## Troubleshooting

### Common Issues

**DNS Resolution Fails**
- Check internet connectivity
- Verify DNS server configuration
- Try alternative DNS servers

**Port Scan Timeout**
- Target may have firewall blocking scans
- Increase timeout values in configuration
- Use different scan techniques

**SSL Certificate Errors**
- Certificate may be self-signed or expired
- Check certificate chain validity
- Verify hostname matches certificate

**Web Interface Not Loading**
- Ensure port 8080 is available
- Check firewall settings
- Verify Python web server is running

### Performance Optimization
- Use quick scan for faster results
- Limit port range for faster scanning
- Run scans during off-peak hours
- Consider network bandwidth limitations

## Security Considerations

- This tool is for authorized security testing only
- Obtain proper permission before scanning external systems
- Be aware of rate limiting and blocking mechanisms
- Use responsibly and ethically
- Follow applicable laws and regulations

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
