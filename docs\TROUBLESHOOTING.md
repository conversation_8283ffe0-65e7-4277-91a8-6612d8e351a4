# Troubleshooting Guide

This guide helps resolve common issues encountered when using the Cybersecurity Scanner.

## Common Issues and Solutions

### 1. DNS Resolution Problems

#### Issue: "DNS resolution failed" or "Name or service not known"

**Symptoms:**
- DNS health checks fail
- Cannot resolve target domain
- Timeout errors during DNS queries

**Solutions:**

1. **Check Internet Connection**
   ```bash
   ping google.com
   nslookup google.com
   ```

2. **Verify DNS Server Configuration**
   ```bash
   # Windows
   ipconfig /all
   nslookup google.com *******
   
   # Linux/macOS
   cat /etc/resolv.conf
   dig @******* google.com
   ```

3. **Use Alternative DNS Servers**
   ```python
   # Modify cybersecurity_orchestrator.py
   import dns.resolver
   resolver = dns.resolver.Resolver()
   resolver.nameservers = ['*******', '*******']
   ```

4. **Check Firewall Settings**
   ```bash
   # Windows
   netsh advfirewall show allprofiles
   
   # Linux
   sudo ufw status
   sudo iptables -L
   ```

### 2. Port Scanning Issues

#### Issue: "Connection timed out" or "No open ports found"

**Symptoms:**
- Port scans timeout
- All ports appear closed
- Slow scanning performance

**Solutions:**

1. **Increase Timeout Values**
   ```python
   # In port_scanner.py, modify timeout
   socket.settimeout(10)  # Increase from default 3 seconds
   ```

2. **Check Target Firewall**
   ```bash
   # Test specific port manually
   telnet target.com 80
   nc -zv target.com 80
   ```

3. **Use Different Scanning Methods**
   ```bash
   # Try nmap directly
   nmap -p 80,443 target.com
   
   # Use UDP scanning for DNS
   nmap -sU -p 53 target.com
   ```

4. **Reduce Concurrent Connections**
   ```python
   # Modify MAX_THREADS in port_scanner.py
   MAX_THREADS = 10  # Reduce from default 50
   ```

### 3. SSL/TLS Certificate Problems

#### Issue: "SSL certificate verification failed" or "Certificate expired"

**Symptoms:**
- SSL checks fail with certificate errors
- HTTPS connections timeout
- Certificate chain validation errors

**Solutions:**

1. **Check Certificate Manually**
   ```bash
   # Using OpenSSL
   openssl s_client -connect target.com:443 -servername target.com
   
   # Check certificate expiry
   echo | openssl s_client -connect target.com:443 2>/dev/null | openssl x509 -noout -dates
   ```

2. **Handle Self-Signed Certificates**
   ```python
   # In ssl_checker.py, add option to ignore verification
   import ssl
   context = ssl.create_default_context()
   context.check_hostname = False
   context.verify_mode = ssl.CERT_NONE
   ```

3. **Update Certificate Store**
   ```bash
   # Ubuntu/Debian
   sudo apt update && sudo apt install ca-certificates
   
   # CentOS/RHEL
   sudo yum update ca-certificates
   
   # Windows
   certlm.msc  # Update through Certificate Manager
   ```

### 4. HTTP Security Analysis Failures

#### Issue: "HTTP request failed" or "Connection refused"

**Symptoms:**
- HTTP security checks timeout
- Cannot retrieve security headers
- 403/404 errors on security endpoints

**Solutions:**

1. **Check HTTP vs HTTPS**
   ```python
   # Try both protocols
   urls = [f'http://{target}', f'https://{target}']
   for url in urls:
       try:
           response = requests.get(url, timeout=10)
           break
       except:
           continue
   ```

2. **Handle User-Agent Blocking**
   ```python
   headers = {
       'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
   }
   response = requests.get(url, headers=headers)
   ```

3. **Follow Redirects**
   ```python
   response = requests.get(url, allow_redirects=True, timeout=10)
   ```

### 5. Email Security Validation Issues

#### Issue: "No MX records found" or "SMTP connection failed"

**Symptoms:**
- Email security checks fail
- Cannot find mail servers
- SMTP timeout errors

**Solutions:**

1. **Check MX Records Manually**
   ```bash
   dig MX target.com
   nslookup -type=MX target.com
   ```

2. **Test SMTP Connection**
   ```bash
   telnet mail.target.com 25
   nc -zv mail.target.com 25
   ```

3. **Handle SMTP Authentication**
   ```python
   # In email_security.py, add authentication
   import smtplib
   server = smtplib.SMTP('smtp.target.com', 587)
   server.starttls()
   # server.login(username, password)  # If required
   ```

### 6. Web Interface Problems

#### Issue: "Cannot connect to web server" or "Page not loading"

**Symptoms:**
- Web interface doesn't load
- API endpoints return errors
- JavaScript console errors

**Solutions:**

1. **Check Server Status**
   ```bash
   python web_server.py
   # Look for "Server running on http://localhost:8080"
   ```

2. **Verify Port Availability**
   ```bash
   # Windows
   netstat -an | findstr :8080
   
   # Linux/macOS
   netstat -an | grep :8080
   lsof -i :8080
   ```

3. **Check Firewall Rules**
   ```bash
   # Windows
   netsh advfirewall firewall add rule name="CyberSec Scanner" dir=in action=allow protocol=TCP localport=8080
   
   # Linux
   sudo ufw allow 8080
   ```

4. **Browser Console Debugging**
   - Open browser developer tools (F12)
   - Check Console tab for JavaScript errors
   - Check Network tab for failed requests

### 7. Performance Issues

#### Issue: "Scans are very slow" or "High CPU/memory usage"

**Symptoms:**
- Scans take much longer than expected
- System becomes unresponsive
- Memory usage keeps increasing

**Solutions:**

1. **Reduce Scan Scope**
   ```bash
   # Use quick scan instead of comprehensive
   python cybersecurity_orchestrator.py target.com --scan-type quick
   ```

2. **Limit Concurrent Operations**
   ```python
   # In configuration, reduce thread counts
   MAX_THREADS = 5  # Reduce from default values
   MAX_CONCURRENT_SCANS = 2
   ```

3. **Monitor Resource Usage**
   ```bash
   # Windows
   tasklist | findstr python
   
   # Linux/macOS
   top -p $(pgrep python)
   htop
   ```

4. **Optimize Network Settings**
   ```python
   # Reduce timeouts for faster failure detection
   SOCKET_TIMEOUT = 3
   HTTP_TIMEOUT = 5
   DNS_TIMEOUT = 2
   ```

### 8. Permission and Access Issues

#### Issue: "Permission denied" or "Access forbidden"

**Symptoms:**
- Cannot write to results directory
- Cannot bind to network ports
- Cannot execute system commands

**Solutions:**

1. **Check File Permissions**
   ```bash
   # Create results directory with proper permissions
   mkdir -p results
   chmod 755 results
   
   # Windows
   icacls results /grant Users:F
   ```

2. **Run with Appropriate Privileges**
   ```bash
   # Linux/macOS (for port scanning)
   sudo python cybersecurity_orchestrator.py target.com
   
   # Windows (run as Administrator)
   # Right-click Command Prompt -> "Run as administrator"
   ```

3. **Use Alternative Ports**
   ```python
   # If port 8080 is restricted, use alternative
   python web_server.py --port 8081
   ```

### 9. Dependency and Import Errors

#### Issue: "ModuleNotFoundError" or "ImportError"

**Symptoms:**
- Python modules cannot be imported
- Missing dependency errors
- Version compatibility issues

**Solutions:**

1. **Verify Installation**
   ```bash
   pip list | grep requests
   pip show dnspython
   ```

2. **Reinstall Dependencies**
   ```bash
   pip uninstall -r requirements.txt -y
   pip install -r requirements.txt
   ```

3. **Check Python Path**
   ```python
   import sys
   print(sys.path)
   print(sys.version)
   ```

4. **Use Virtual Environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # Linux/macOS
   venv\Scripts\activate     # Windows
   pip install -r requirements.txt
   ```

### 10. Network Connectivity Issues

#### Issue: "Network unreachable" or "Connection refused"

**Symptoms:**
- Cannot reach external targets
- All network operations fail
- Proxy or firewall blocking

**Solutions:**

1. **Test Basic Connectivity**
   ```bash
   ping *******
   curl -I https://google.com
   ```

2. **Configure Proxy Settings**
   ```python
   # Add proxy configuration
   proxies = {
       'http': 'http://proxy.company.com:8080',
       'https': 'https://proxy.company.com:8080'
   }
   response = requests.get(url, proxies=proxies)
   ```

3. **Check Corporate Firewall**
   ```bash
   # Test specific ports
   telnet google.com 80
   telnet google.com 443
   ```

## Debugging Tips

### Enable Debug Logging

1. **Modify Logging Level**
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

2. **Add Custom Debug Output**
   ```python
   print(f"DEBUG: Scanning {target} with options {scan_options}")
   ```

### Capture Network Traffic

1. **Using Wireshark**
   - Install Wireshark
   - Capture on network interface
   - Filter by target IP or domain

2. **Using tcpdump (Linux/macOS)**
   ```bash
   sudo tcpdump -i any host target.com
   ```

### Test Individual Components

1. **Test DNS Module**
   ```python
   from python_modules.dns_checker import check_dns_health
   result = check_dns_health("google.com")
   print(result)
   ```

2. **Test Port Scanner**
   ```python
   from python_modules.port_scanner import scan_ports
   result = scan_ports("google.com", [80, 443])
   print(result)
   ```

## Getting Help

### Log Files
Check the following locations for log files:
- `scanner.log` (if configured)
- System logs: `/var/log/` (Linux), Event Viewer (Windows)
- Python error output in terminal

### Reporting Issues
When reporting issues, include:
1. Operating system and version
2. Python version
3. Complete error message
4. Steps to reproduce
5. Target being scanned (if not sensitive)
6. Scan type and options used

### Community Resources
- GitHub Issues: Report bugs and feature requests
- Documentation: Check latest documentation updates
- Stack Overflow: Search for similar issues

### Professional Support
For enterprise deployments:
- Performance optimization consulting
- Custom feature development
- Security audit and compliance
- Training and implementation support
