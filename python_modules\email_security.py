#!/usr/bin/env python3
"""
Email Security Checker Module
Checks email security configurations including SPF, DKIM, DMARC, and SMTP relay
"""

import dns.resolver
import smtplib
import socket
import json
import datetime
import re
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

class EmailSecurityChecker:
    def __init__(self):
        self.timeout = 10
        self.dns_servers = ['*******', '*******']
    
    def check_spf_record(self, domain):
        """Check SPF (Sender Policy Framework) record"""
        result = {
            "domain": domain,
            "timestamp": datetime.datetime.now().isoformat(),
            "spf_record": None,
            "spf_valid": False,
            "spf_mechanisms": [],
            "spf_issues": [],
            "recommendations": []
        }
        
        try:
            # Query TXT records for SPF
            answers = dns.resolver.resolve(domain, 'TXT')
            
            for rdata in answers:
                txt_record = str(rdata).strip('"')
                if txt_record.startswith('v=spf1'):
                    result["spf_record"] = txt_record
                    result["spf_valid"] = True
                    result.update(self._parse_spf_record(txt_record))
                    break
            
            if not result["spf_record"]:
                result["spf_issues"].append("No SPF record found")
                result["recommendations"].append("Add SPF record to prevent email spoofing")
        
        except dns.resolver.NXDOMAIN:
            result["error"] = "Domain not found"
        except Exception as e:
            result["error"] = str(e)
        
        return result
    
    def _parse_spf_record(self, spf_record):
        """Parse SPF record mechanisms and qualifiers"""
        result = {
            "spf_mechanisms": [],
            "spf_issues": [],
            "recommendations": []
        }
        
        # Split SPF record into mechanisms
        parts = spf_record.split()
        
        for part in parts[1:]:  # Skip 'v=spf1'
            if part.startswith(('include:', 'a:', 'mx:', 'ip4:', 'ip6:', 'exists:')):
                mechanism_type = part.split(':')[0]
                mechanism_value = part.split(':', 1)[1] if ':' in part else ''
                
                result["spf_mechanisms"].append({
                    "type": mechanism_type,
                    "value": mechanism_value,
                    "qualifier": "pass"  # Default qualifier
                })
            
            elif part in ['a', 'mx']:
                result["spf_mechanisms"].append({
                    "type": part,
                    "value": "",
                    "qualifier": "pass"
                })
            
            elif part.startswith(('-', '~', '?')):
                qualifier = part[0]
                mechanism = part[1:]
                result["spf_mechanisms"].append({
                    "type": mechanism.split(':')[0] if ':' in mechanism else mechanism,
                    "value": mechanism.split(':', 1)[1] if ':' in mechanism else '',
                    "qualifier": qualifier
                })
            
            elif part == 'all':
                result["spf_mechanisms"].append({
                    "type": "all",
                    "value": "",
                    "qualifier": "pass"
                })
            
            elif part in ['-all', '~all', '?all']:
                qualifier = part[0] if part[0] in ['-', '~', '?'] else 'pass'
                result["spf_mechanisms"].append({
                    "type": "all",
                    "value": "",
                    "qualifier": qualifier
                })
        
        # Check for common issues
        all_mechanisms = [m for m in result["spf_mechanisms"] if m["type"] == "all"]
        if not all_mechanisms:
            result["spf_issues"].append("Missing 'all' mechanism")
            result["recommendations"].append("Add '-all' or '~all' at the end of SPF record")
        elif all_mechanisms[0]["qualifier"] == "pass":
            result["spf_issues"].append("Permissive 'all' mechanism")
            result["recommendations"].append("Use '-all' instead of 'all' for stricter policy")
        
        # Check for too many DNS lookups
        dns_lookup_mechanisms = [m for m in result["spf_mechanisms"] 
                               if m["type"] in ["include", "a", "mx", "exists"]]
        if len(dns_lookup_mechanisms) > 10:
            result["spf_issues"].append("Too many DNS lookups (>10)")
            result["recommendations"].append("Reduce DNS lookups to avoid SPF record limit")
        
        return result
    
    def check_dkim_record(self, domain, selector="default"):
        """Check DKIM (DomainKeys Identified Mail) record"""
        result = {
            "domain": domain,
            "selector": selector,
            "timestamp": datetime.datetime.now().isoformat(),
            "dkim_record": None,
            "dkim_valid": False,
            "public_key": None,
            "key_algorithm": None,
            "key_length": None,
            "dkim_issues": [],
            "recommendations": []
        }
        
        try:
            # Query DKIM record
            dkim_domain = f"{selector}._domainkey.{domain}"
            answers = dns.resolver.resolve(dkim_domain, 'TXT')
            
            for rdata in answers:
                txt_record = str(rdata).strip('"')
                if 'p=' in txt_record:  # DKIM record contains public key
                    result["dkim_record"] = txt_record
                    result["dkim_valid"] = True
                    result.update(self._parse_dkim_record(txt_record))
                    break
            
            if not result["dkim_record"]:
                result["dkim_issues"].append("No DKIM record found")
                result["recommendations"].append("Configure DKIM signing for email authentication")
        
        except dns.resolver.NXDOMAIN:
            result["dkim_issues"].append("DKIM record not found")
            result["recommendations"].append("Set up DKIM record for email authentication")
        except Exception as e:
            result["error"] = str(e)
        
        return result
    
    def _parse_dkim_record(self, dkim_record):
        """Parse DKIM record parameters"""
        result = {
            "public_key": None,
            "key_algorithm": "rsa",  # Default
            "key_length": None,
            "dkim_issues": [],
            "recommendations": []
        }
        
        # Parse DKIM parameters
        params = {}
        for param in dkim_record.split(';'):
            param = param.strip()
            if '=' in param:
                key, value = param.split('=', 1)
                params[key.strip()] = value.strip()
        
        # Extract public key
        if 'p' in params:
            result["public_key"] = params['p']
            
            # Estimate key length (rough approximation)
            key_data = params['p'].replace(' ', '')
            if key_data:
                # Base64 encoded key length estimation
                estimated_bits = len(key_data) * 6 // 8 * 8  # Rough estimate
                result["key_length"] = estimated_bits
                
                if estimated_bits < 1024:
                    result["dkim_issues"].append("Weak key length")
                    result["recommendations"].append("Use at least 2048-bit RSA key")
        
        # Check algorithm
        if 'k' in params:
            result["key_algorithm"] = params['k']
        
        # Check for revoked key
        if 'p' in params and not params['p'].strip():
            result["dkim_issues"].append("DKIM key is revoked")
        
        return result
    
    def check_dmarc_record(self, domain):
        """Check DMARC (Domain-based Message Authentication) record"""
        result = {
            "domain": domain,
            "timestamp": datetime.datetime.now().isoformat(),
            "dmarc_record": None,
            "dmarc_valid": False,
            "policy": None,
            "subdomain_policy": None,
            "percentage": 100,
            "alignment": {},
            "reporting": {},
            "dmarc_issues": [],
            "recommendations": []
        }
        
        try:
            # Query DMARC record
            dmarc_domain = f"_dmarc.{domain}"
            answers = dns.resolver.resolve(dmarc_domain, 'TXT')
            
            for rdata in answers:
                txt_record = str(rdata).strip('"')
                if txt_record.startswith('v=DMARC1'):
                    result["dmarc_record"] = txt_record
                    result["dmarc_valid"] = True
                    result.update(self._parse_dmarc_record(txt_record))
                    break
            
            if not result["dmarc_record"]:
                result["dmarc_issues"].append("No DMARC record found")
                result["recommendations"].append("Implement DMARC policy for email protection")
        
        except dns.resolver.NXDOMAIN:
            result["dmarc_issues"].append("DMARC record not found")
            result["recommendations"].append("Set up DMARC record for email authentication")
        except Exception as e:
            result["error"] = str(e)
        
        return result
    
    def _parse_dmarc_record(self, dmarc_record):
        """Parse DMARC record parameters"""
        result = {
            "policy": None,
            "subdomain_policy": None,
            "percentage": 100,
            "alignment": {"spf": "relaxed", "dkim": "relaxed"},
            "reporting": {},
            "dmarc_issues": [],
            "recommendations": []
        }
        
        # Parse DMARC parameters
        params = {}
        for param in dmarc_record.split(';'):
            param = param.strip()
            if '=' in param:
                key, value = param.split('=', 1)
                params[key.strip()] = value.strip()
        
        # Extract policy
        if 'p' in params:
            result["policy"] = params['p']
            if params['p'] == 'none':
                result["dmarc_issues"].append("DMARC policy is set to 'none' (monitoring only)")
                result["recommendations"].append("Consider upgrading to 'quarantine' or 'reject' policy")
        
        # Extract subdomain policy
        if 'sp' in params:
            result["subdomain_policy"] = params['sp']
        
        # Extract percentage
        if 'pct' in params:
            try:
                result["percentage"] = int(params['pct'])
                if result["percentage"] < 100:
                    result["dmarc_issues"].append(f"DMARC policy applies to only {result['percentage']}% of emails")
            except ValueError:
                pass
        
        # Extract alignment modes
        if 'aspf' in params:
            result["alignment"]["spf"] = params['aspf']
        if 'adkim' in params:
            result["alignment"]["dkim"] = params['adkim']
        
        # Extract reporting addresses
        if 'rua' in params:
            result["reporting"]["aggregate"] = params['rua']
        if 'ruf' in params:
            result["reporting"]["forensic"] = params['ruf']
        
        return result
    
    def check_mx_records(self, domain):
        """Check MX (Mail Exchange) records"""
        result = {
            "domain": domain,
            "timestamp": datetime.datetime.now().isoformat(),
            "mx_records": [],
            "mail_servers": [],
            "mx_issues": [],
            "recommendations": []
        }
        
        try:
            answers = dns.resolver.resolve(domain, 'MX')
            
            for rdata in answers:
                mx_record = {
                    "priority": rdata.preference,
                    "exchange": str(rdata.exchange).rstrip('.')
                }
                result["mx_records"].append(mx_record)
                result["mail_servers"].append(mx_record["exchange"])
            
            # Sort by priority
            result["mx_records"].sort(key=lambda x: x["priority"])
            
            # Check for common issues
            if len(result["mx_records"]) == 1:
                result["mx_issues"].append("Single MX record (no redundancy)")
                result["recommendations"].append("Consider adding backup MX records")
            
            # Check for null MX record
            for mx in result["mx_records"]:
                if mx["exchange"] == ".":
                    result["mx_issues"].append("Null MX record found (mail disabled)")
        
        except dns.resolver.NXDOMAIN:
            result["error"] = "Domain not found"
        except dns.resolver.NoAnswer:
            result["mx_issues"].append("No MX records found")
            result["recommendations"].append("Configure MX records for email delivery")
        except Exception as e:
            result["error"] = str(e)
        
        return result
    
    def test_smtp_relay(self, mail_server, test_email="<EMAIL>"):
        """Test SMTP relay configuration"""
        result = {
            "mail_server": mail_server,
            "test_email": test_email,
            "timestamp": datetime.datetime.now().isoformat(),
            "smtp_available": False,
            "relay_allowed": False,
            "auth_required": False,
            "tls_supported": False,
            "smtp_banner": None,
            "smtp_issues": [],
            "recommendations": []
        }
        
        try:
            # Connect to SMTP server
            server = smtplib.SMTP(mail_server, 25, timeout=self.timeout)
            result["smtp_available"] = True
            result["smtp_banner"] = server.getwelcome()
            
            # Check for STARTTLS support
            if server.has_extn('STARTTLS'):
                result["tls_supported"] = True
                try:
                    server.starttls()
                except:
                    result["smtp_issues"].append("STARTTLS advertised but failed")
            else:
                result["smtp_issues"].append("STARTTLS not supported")
                result["recommendations"].append("Enable STARTTLS for secure email transmission")
            
            # Test relay
            try:
                server.mail(test_email)
                server.rcpt("<EMAIL>")
                result["relay_allowed"] = True
                result["smtp_issues"].append("Open relay detected")
                result["recommendations"].append("Disable open relay to prevent spam abuse")
            except smtplib.SMTPRecipientsRefused:
                result["relay_allowed"] = False  # Good - relay is restricted
            except smtplib.SMTPAuthenticationError:
                result["auth_required"] = True
            
            server.quit()
        
        except socket.timeout:
            result["error"] = "Connection timeout"
        except Exception as e:
            result["error"] = str(e)
        
        return result
    
    def comprehensive_email_check(self, domain):
        """Perform comprehensive email security check"""
        result = {
            "domain": domain,
            "timestamp": datetime.datetime.now().isoformat(),
            "overall_score": 0,
            "spf": self.check_spf_record(domain),
            "dkim": self.check_dkim_record(domain),
            "dmarc": self.check_dmarc_record(domain),
            "mx": self.check_mx_records(domain),
            "smtp_tests": [],
            "summary": {
                "total_issues": 0,
                "critical_issues": 0,
                "recommendations": []
            }
        }
        
        # Test SMTP for each MX record
        for mx_record in result["mx"]["mx_records"]:
            smtp_result = self.test_smtp_relay(mx_record["exchange"])
            result["smtp_tests"].append(smtp_result)
        
        # Calculate overall score
        score = 0
        if result["spf"]["spf_valid"]:
            score += 25
        if result["dkim"]["dkim_valid"]:
            score += 25
        if result["dmarc"]["dmarc_valid"]:
            score += 30
        if result["mx"]["mx_records"]:
            score += 20
        
        result["overall_score"] = score
        
        # Collect all issues and recommendations
        all_issues = []
        all_recommendations = []
        
        for check_name in ["spf", "dkim", "dmarc", "mx"]:
            check_result = result[check_name]
            issues_key = f"{check_name}_issues"
            if issues_key in check_result:
                all_issues.extend(check_result[issues_key])
            if "recommendations" in check_result:
                all_recommendations.extend(check_result["recommendations"])
        
        for smtp_test in result["smtp_tests"]:
            if "smtp_issues" in smtp_test:
                all_issues.extend(smtp_test["smtp_issues"])
            if "recommendations" in smtp_test:
                all_recommendations.extend(smtp_test["recommendations"])
        
        result["summary"]["total_issues"] = len(all_issues)
        result["summary"]["critical_issues"] = len([i for i in all_issues if "open relay" in i.lower() or "expired" in i.lower()])
        result["summary"]["recommendations"] = list(set(all_recommendations))
        
        return result

def main():
    """Test the email security checker"""
    checker = EmailSecurityChecker()
    
    domain = "google.com"
    
    print(f"Testing email security for {domain}...")
    
    # Comprehensive check
    result = checker.comprehensive_email_check(domain)
    print(json.dumps(result, indent=2))

if __name__ == "__main__":
    main()
