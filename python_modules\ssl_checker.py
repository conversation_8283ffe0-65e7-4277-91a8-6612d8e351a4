#!/usr/bin/env python3
"""
SSL/TLS Certificate Checker Module
Performs SSL certificate validation and security checks
"""

import ssl
import socket
import datetime
import json
import requests
from urllib.parse import urlparse
from cryptography import x509
from cryptography.hazmat.backends import default_backend
# import OpenSSL  # Optional, using cryptography instead

class SSLChecker:
    def __init__(self):
        self.timeout = 10
    
    def check_ssl_certificate(self, domain, port=443):
        """Check SSL certificate for a domain"""
        result = {
            "domain": domain,
            "port": port,
            "timestamp": datetime.datetime.now().isoformat(),
            "ssl_enabled": False,
            "certificate_valid": False,
            "certificate_info": {},
            "security_issues": [],
            "recommendations": []
        }
        
        try:
            # Create SSL context
            context = ssl.create_default_context()
            
            # Connect and get certificate
            with socket.create_connection((domain, port), timeout=self.timeout) as sock:
                with context.wrap_socket(sock, server_hostname=domain) as ssock:
                    result["ssl_enabled"] = True
                    
                    # Get certificate
                    cert_der = ssock.getpeercert(binary_form=True)
                    cert_pem = ssock.getpeercert()
                    
                    if cert_der and cert_pem:
                        result["certificate_valid"] = True
                        result["certificate_info"] = self._parse_certificate(cert_der, cert_pem)
                        result["security_issues"] = self._check_certificate_security(cert_pem, ssock)
                        result["recommendations"] = self._generate_ssl_recommendations(result["security_issues"])
        
        except ssl.SSLError as e:
            result["error"] = f"SSL Error: {str(e)}"
            result["security_issues"].append("SSL connection failed")
        except socket.timeout:
            result["error"] = "Connection timeout"
        except Exception as e:
            result["error"] = f"Connection error: {str(e)}"
        
        return result
    
    def _parse_certificate(self, cert_der, cert_pem):
        """Parse certificate information"""
        try:
            # Parse with cryptography library
            cert = x509.load_der_x509_certificate(cert_der, default_backend())
            
            info = {
                "subject": dict(x.split('=') for x in cert_pem['subject'][0]),
                "issuer": dict(x.split('=') for x in cert_pem['issuer'][0]),
                "version": cert.version.name,
                "serial_number": str(cert.serial_number),
                "not_before": cert.not_valid_before.isoformat(),
                "not_after": cert.not_valid_after.isoformat(),
                "signature_algorithm": cert.signature_algorithm_oid._name,
                "public_key_algorithm": cert.public_key().__class__.__name__,
                "san": self._get_san_list(cert),
                "key_size": self._get_key_size(cert),
                "days_until_expiry": (cert.not_valid_after - datetime.datetime.now()).days
            }
            
            return info
        except Exception as e:
            return {"error": f"Certificate parsing failed: {str(e)}"}
    
    def _get_san_list(self, cert):
        """Get Subject Alternative Names"""
        try:
            san_ext = cert.extensions.get_extension_for_oid(x509.ExtensionOID.SUBJECT_ALTERNATIVE_NAME)
            return [name.value for name in san_ext.value]
        except:
            return []
    
    def _get_key_size(self, cert):
        """Get public key size"""
        try:
            public_key = cert.public_key()
            if hasattr(public_key, 'key_size'):
                return public_key.key_size
            return None
        except:
            return None
    
    def _check_certificate_security(self, cert_pem, ssock):
        """Check for security issues"""
        issues = []
        
        # Check expiry
        not_after = datetime.datetime.strptime(cert_pem['notAfter'], '%b %d %H:%M:%S %Y %Z')
        days_left = (not_after - datetime.datetime.now()).days
        
        if days_left < 0:
            issues.append("Certificate has expired")
        elif days_left < 30:
            issues.append(f"Certificate expires in {days_left} days")
        
        # Check cipher suite
        cipher = ssock.cipher()
        if cipher:
            cipher_name = cipher[0]
            if 'RC4' in cipher_name or 'DES' in cipher_name:
                issues.append(f"Weak cipher suite: {cipher_name}")
            if cipher[2] < 128:
                issues.append(f"Weak encryption: {cipher[2]} bits")
        
        # Check protocol version
        protocol = ssock.version()
        if protocol in ['SSLv2', 'SSLv3', 'TLSv1', 'TLSv1.1']:
            issues.append(f"Outdated protocol: {protocol}")
        
        return issues
    
    def _generate_ssl_recommendations(self, issues):
        """Generate recommendations based on issues"""
        recommendations = []
        
        for issue in issues:
            if "expired" in issue.lower():
                recommendations.append("Renew SSL certificate immediately")
            elif "expires" in issue.lower():
                recommendations.append("Plan certificate renewal")
            elif "weak cipher" in issue.lower():
                recommendations.append("Configure stronger cipher suites")
            elif "weak encryption" in issue.lower():
                recommendations.append("Use encryption with at least 256 bits")
            elif "outdated protocol" in issue.lower():
                recommendations.append("Upgrade to TLS 1.2 or higher")
        
        if not issues:
            recommendations.append("SSL configuration appears secure")
        
        return recommendations
    
    def check_hsts_header(self, url):
        """Check for HSTS (HTTP Strict Transport Security) header"""
        result = {
            "url": url,
            "hsts_enabled": False,
            "hsts_header": None,
            "max_age": None,
            "include_subdomains": False,
            "preload": False
        }
        
        try:
            response = requests.get(url, timeout=self.timeout, allow_redirects=True)
            hsts_header = response.headers.get('Strict-Transport-Security')
            
            if hsts_header:
                result["hsts_enabled"] = True
                result["hsts_header"] = hsts_header
                
                # Parse HSTS header
                parts = hsts_header.split(';')
                for part in parts:
                    part = part.strip()
                    if part.startswith('max-age='):
                        result["max_age"] = int(part.split('=')[1])
                    elif part == 'includeSubDomains':
                        result["include_subdomains"] = True
                    elif part == 'preload':
                        result["preload"] = True
        
        except Exception as e:
            result["error"] = str(e)
        
        return result
    
    def check_ocsp_crl(self, domain, port=443):
        """Check OCSP and CRL URLs in certificate"""
        result = {
            "domain": domain,
            "ocsp_urls": [],
            "crl_urls": [],
            "ocsp_available": False,
            "crl_available": False
        }
        
        try:
            # Get certificate
            context = ssl.create_default_context()
            with socket.create_connection((domain, port), timeout=self.timeout) as sock:
                with context.wrap_socket(sock, server_hostname=domain) as ssock:
                    cert_der = ssock.getpeercert(binary_form=True)
                    
                    if cert_der:
                        cert = x509.load_der_x509_certificate(cert_der, default_backend())
                        
                        # Check for Authority Information Access extension
                        try:
                            aia_ext = cert.extensions.get_extension_for_oid(x509.ExtensionOID.AUTHORITY_INFORMATION_ACCESS)
                            for access_desc in aia_ext.value:
                                if access_desc.access_method == x509.AuthorityInformationAccessOID.OCSP:
                                    result["ocsp_urls"].append(access_desc.access_location.value)
                                    result["ocsp_available"] = True
                        except:
                            pass
                        
                        # Check for CRL Distribution Points extension
                        try:
                            crl_ext = cert.extensions.get_extension_for_oid(x509.ExtensionOID.CRL_DISTRIBUTION_POINTS)
                            for dist_point in crl_ext.value:
                                if dist_point.full_name:
                                    for name in dist_point.full_name:
                                        result["crl_urls"].append(name.value)
                                        result["crl_available"] = True
                        except:
                            pass
        
        except Exception as e:
            result["error"] = str(e)
        
        return result
    
    def get_tls_version(self, domain, port=443):
        """Determine supported TLS versions"""
        result = {
            "domain": domain,
            "supported_versions": [],
            "recommended_version": None,
            "security_issues": []
        }
        
        # Test different TLS versions
        versions_to_test = [
            (ssl.PROTOCOL_TLS, "TLS (Auto)"),
            (ssl.PROTOCOL_TLSv1_2, "TLSv1.2"),
        ]
        
        # Add TLSv1.3 if available
        if hasattr(ssl, 'PROTOCOL_TLSv1_3'):
            versions_to_test.append((ssl.PROTOCOL_TLSv1_3, "TLSv1.3"))
        
        for protocol, version_name in versions_to_test:
            try:
                context = ssl.SSLContext(protocol)
                with socket.create_connection((domain, port), timeout=self.timeout) as sock:
                    with context.wrap_socket(sock, server_hostname=domain) as ssock:
                        actual_version = ssock.version()
                        if actual_version and actual_version not in result["supported_versions"]:
                            result["supported_versions"].append(actual_version)
            except:
                continue
        
        # Determine recommended version and issues
        if result["supported_versions"]:
            if "TLSv1.3" in result["supported_versions"]:
                result["recommended_version"] = "TLSv1.3"
            elif "TLSv1.2" in result["supported_versions"]:
                result["recommended_version"] = "TLSv1.2"
            
            # Check for security issues
            for version in result["supported_versions"]:
                if version in ["SSLv2", "SSLv3", "TLSv1", "TLSv1.1"]:
                    result["security_issues"].append(f"Insecure protocol supported: {version}")
        
        return result

def main():
    """Test the SSL checker"""
    checker = SSLChecker()
    
    # Test domain
    domain = "google.com"
    
    print(f"Testing SSL for {domain}...")
    
    # Check SSL certificate
    ssl_result = checker.check_ssl_certificate(domain)
    print(json.dumps(ssl_result, indent=2))
    
    # Check HSTS
    hsts_result = checker.check_hsts_header(f"https://{domain}")
    print(json.dumps(hsts_result, indent=2))

if __name__ == "__main__":
    main()
