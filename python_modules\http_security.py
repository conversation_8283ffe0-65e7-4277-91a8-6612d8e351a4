#!/usr/bin/env python3
"""
HTTP Security Headers Checker Module
Analyzes HTTP security headers and web configuration
"""

import requests
import json
import datetime
import re
from urllib.parse import urlparse, urljoin
from bs4 import BeautifulSoup
import hashlib

class HTTPSecurityChecker:
    def __init__(self):
        self.timeout = 10
        self.user_agent = "CyberSecurity-Scanner/1.0"
        self.session = requests.Session()
        self.session.headers.update({'User-Agent': self.user_agent})
    
    def check_security_headers(self, url):
        """Check HTTP security headers"""
        result = {
            "url": url,
            "timestamp": datetime.datetime.now().isoformat(),
            "headers": {},
            "security_score": 0,
            "missing_headers": [],
            "weak_headers": [],
            "recommendations": []
        }
        
        try:
            response = self.session.get(url, timeout=self.timeout, allow_redirects=True)
            result["status_code"] = response.status_code
            result["headers"] = dict(response.headers)
            
            # Check security headers
            security_headers = {
                'Strict-Transport-Security': self._check_hsts,
                'X-Frame-Options': self._check_x_frame_options,
                'X-Content-Type-Options': self._check_x_content_type_options,
                'X-XSS-Protection': self._check_x_xss_protection,
                'Content-Security-Policy': self._check_csp,
                'Referrer-Policy': self._check_referrer_policy,
                'Permissions-Policy': self._check_permissions_policy,
                'X-Permitted-Cross-Domain-Policies': self._check_cross_domain_policies
            }
            
            for header_name, check_func in security_headers.items():
                header_value = response.headers.get(header_name)
                if header_value:
                    check_result = check_func(header_value)
                    result[header_name.lower().replace('-', '_')] = check_result
                    if check_result.get('secure', False):
                        result["security_score"] += 10
                    elif check_result.get('weak', False):
                        result["weak_headers"].append(header_name)
                        result["security_score"] += 5
                else:
                    result["missing_headers"].append(header_name)
            
            # Generate recommendations
            result["recommendations"] = self._generate_header_recommendations(result)
            
        except Exception as e:
            result["error"] = str(e)
        
        return result
    
    def _check_hsts(self, value):
        """Check HSTS header"""
        result = {"value": value, "secure": False, "issues": []}
        
        if 'max-age=' in value:
            max_age_match = re.search(r'max-age=(\d+)', value)
            if max_age_match:
                max_age = int(max_age_match.group(1))
                if max_age >= 31536000:  # 1 year
                    result["secure"] = True
                else:
                    result["issues"].append(f"max-age too short: {max_age} seconds")
        
        if 'includeSubDomains' in value:
            result["include_subdomains"] = True
        else:
            result["issues"].append("includeSubDomains not set")
        
        return result
    
    def _check_x_frame_options(self, value):
        """Check X-Frame-Options header"""
        result = {"value": value, "secure": False}
        
        if value.upper() in ['DENY', 'SAMEORIGIN']:
            result["secure"] = True
        elif value.upper().startswith('ALLOW-FROM'):
            result["weak"] = True
            result["issues"] = ["ALLOW-FROM is deprecated, use CSP frame-ancestors instead"]
        
        return result
    
    def _check_x_content_type_options(self, value):
        """Check X-Content-Type-Options header"""
        result = {"value": value, "secure": False}
        
        if value.lower() == 'nosniff':
            result["secure"] = True
        
        return result
    
    def _check_x_xss_protection(self, value):
        """Check X-XSS-Protection header"""
        result = {"value": value, "secure": False}
        
        if value == '1; mode=block':
            result["secure"] = True
        elif value == '0':
            result["weak"] = True
            result["issues"] = ["XSS protection disabled"]
        
        return result
    
    def _check_csp(self, value):
        """Check Content-Security-Policy header"""
        result = {"value": value, "secure": False, "directives": {}}
        
        # Parse CSP directives
        directives = value.split(';')
        for directive in directives:
            directive = directive.strip()
            if ' ' in directive:
                key, val = directive.split(' ', 1)
                result["directives"][key] = val
        
        # Check for secure directives
        secure_count = 0
        if 'default-src' in result["directives"]:
            secure_count += 1
        if 'script-src' in result["directives"] and "'unsafe-inline'" not in result["directives"]["script-src"]:
            secure_count += 1
        if 'object-src' in result["directives"] and "'none'" in result["directives"]["object-src"]:
            secure_count += 1
        
        if secure_count >= 2:
            result["secure"] = True
        
        return result
    
    def _check_referrer_policy(self, value):
        """Check Referrer-Policy header"""
        result = {"value": value, "secure": False}
        
        secure_policies = ['no-referrer', 'same-origin', 'strict-origin', 'strict-origin-when-cross-origin']
        if value in secure_policies:
            result["secure"] = True
        
        return result
    
    def _check_permissions_policy(self, value):
        """Check Permissions-Policy header"""
        result = {"value": value, "secure": True}  # Presence is good
        return result
    
    def _check_cross_domain_policies(self, value):
        """Check X-Permitted-Cross-Domain-Policies header"""
        result = {"value": value, "secure": False}
        
        if value in ['none', 'master-only']:
            result["secure"] = True
        
        return result
    
    def _generate_header_recommendations(self, result):
        """Generate recommendations based on header analysis"""
        recommendations = []
        
        for header in result["missing_headers"]:
            if header == 'Strict-Transport-Security':
                recommendations.append("Add HSTS header: Strict-Transport-Security: max-age=31536000; includeSubDomains")
            elif header == 'X-Frame-Options':
                recommendations.append("Add X-Frame-Options: DENY or SAMEORIGIN")
            elif header == 'X-Content-Type-Options':
                recommendations.append("Add X-Content-Type-Options: nosniff")
            elif header == 'Content-Security-Policy':
                recommendations.append("Implement Content Security Policy")
        
        for header in result["weak_headers"]:
            recommendations.append(f"Strengthen {header} configuration")
        
        return recommendations
    
    def check_redirect_loops(self, url, max_redirects=10):
        """Check for infinite redirect loops"""
        result = {
            "url": url,
            "has_redirect_loop": False,
            "redirect_chain": [],
            "final_url": url,
            "redirect_count": 0
        }
        
        try:
            visited_urls = set()
            current_url = url
            
            for i in range(max_redirects + 1):
                if current_url in visited_urls:
                    result["has_redirect_loop"] = True
                    break
                
                visited_urls.add(current_url)
                result["redirect_chain"].append(current_url)
                
                response = self.session.get(current_url, timeout=self.timeout, allow_redirects=False)
                
                if response.status_code in [301, 302, 303, 307, 308]:
                    location = response.headers.get('Location')
                    if location:
                        if location.startswith('/'):
                            parsed_url = urlparse(current_url)
                            current_url = f"{parsed_url.scheme}://{parsed_url.netloc}{location}"
                        else:
                            current_url = location
                        result["redirect_count"] += 1
                    else:
                        break
                else:
                    result["final_url"] = current_url
                    break
            
            if result["redirect_count"] >= max_redirects:
                result["has_redirect_loop"] = True
        
        except Exception as e:
            result["error"] = str(e)
        
        return result
    
    def check_directory_listing(self, base_url):
        """Check for directory listing vulnerabilities"""
        result = {
            "base_url": base_url,
            "vulnerable_paths": [],
            "accessible_files": []
        }
        
        # Common paths to check
        test_paths = [
            '/admin/',
            '/backup/',
            '/config/',
            '/logs/',
            '/tmp/',
            '/uploads/',
            '/files/',
            '/data/',
            '/test/',
            '/dev/',
            '/.git/',
            '/.svn/',
            '/wp-admin/',
            '/phpmyadmin/'
        ]
        
        for path in test_paths:
            try:
                test_url = urljoin(base_url, path)
                response = self.session.get(test_url, timeout=self.timeout)
                
                if response.status_code == 200:
                    # Check if it's a directory listing
                    if self._is_directory_listing(response.text):
                        result["vulnerable_paths"].append(test_url)
                    
                    # Check for accessible files
                    files = self._extract_file_links(response.text, test_url)
                    if files:
                        result["accessible_files"].extend(files)
            
            except:
                continue
        
        return result
    
    def _is_directory_listing(self, html_content):
        """Check if content appears to be a directory listing"""
        indicators = [
            'Index of /',
            'Directory Listing',
            'Parent Directory',
            '<title>Index of',
            'Apache/2.',
            'nginx/',
            '[DIR]',
            '[   ]'
        ]
        
        return any(indicator in html_content for indicator in indicators)
    
    def _extract_file_links(self, html_content, base_url):
        """Extract file links from HTML content"""
        files = []
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            links = soup.find_all('a', href=True)
            
            for link in links:
                href = link['href']
                if not href.startswith('http') and not href.startswith('/'):
                    full_url = urljoin(base_url, href)
                    # Check if it looks like a file
                    if '.' in href and not href.endswith('/'):
                        files.append(full_url)
        except:
            pass
        
        return files
    
    def get_favicon_hash(self, url):
        """Get favicon hash for fingerprinting"""
        result = {
            "url": url,
            "favicon_url": None,
            "favicon_hash": None,
            "error": None
        }
        
        try:
            # Try common favicon locations
            favicon_urls = [
                urljoin(url, '/favicon.ico'),
                urljoin(url, '/apple-touch-icon.png'),
                urljoin(url, '/favicon.png')
            ]
            
            # Also check HTML for favicon link
            try:
                response = self.session.get(url, timeout=self.timeout)
                soup = BeautifulSoup(response.text, 'html.parser')
                favicon_link = soup.find('link', rel=lambda x: x and 'icon' in x.lower())
                if favicon_link and favicon_link.get('href'):
                    favicon_urls.insert(0, urljoin(url, favicon_link['href']))
            except:
                pass
            
            # Try to download favicon
            for favicon_url in favicon_urls:
                try:
                    response = self.session.get(favicon_url, timeout=self.timeout)
                    if response.status_code == 200 and response.content:
                        result["favicon_url"] = favicon_url
                        result["favicon_hash"] = hashlib.md5(response.content).hexdigest()
                        break
                except:
                    continue
        
        except Exception as e:
            result["error"] = str(e)
        
        return result

def main():
    """Test the HTTP security checker"""
    checker = HTTPSecurityChecker()
    
    # Test URL
    url = "https://google.com"
    
    print(f"Testing HTTP security for {url}...")
    
    # Check security headers
    headers_result = checker.check_security_headers(url)
    print("Security Headers:")
    print(json.dumps(headers_result, indent=2))
    
    # Check redirect loops
    redirect_result = checker.check_redirect_loops(url)
    print("\nRedirect Check:")
    print(json.dumps(redirect_result, indent=2))

if __name__ == "__main__":
    main()
