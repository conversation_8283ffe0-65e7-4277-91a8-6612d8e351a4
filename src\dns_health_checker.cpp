#include "dns_health_checker.h"
#ifdef HAVE_LDNS
#include <ldns/ldns.h>
#endif
#include <iostream>
#include <sstream>
#include <regex>
#include <algorithm>
#include <iomanip>
#ifdef _WIN32
#include <winsock2.h>
#else
#include <unistd.h>
#endif

namespace dns_health_checker
{

    // Utility functions implementation
    namespace dns_utils
    {
        std::string resultToString(CheckResult result)
        {
            switch (result)
            {
            case CheckResult::PASS:
                return "PASS";
            case CheckResult::FAIL:
                return "FAIL";
            case CheckResult::WARNING:
                return "WARNING";
            case CheckResult::NOT_APPLICABLE:
                return "NOT_APPLICABLE";
            default:
                return "UNKNOWN";
            }
        }

        std::string formatTimestamp(const std::chrono::system_clock::time_point &tp)
        {
            auto time_t = std::chrono::system_clock::to_time_t(tp);
            std::stringstream ss;
            ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
            return ss.str();
        }

        bool isValidIPv4(const std::string &ip)
        {
            std::regex ipv4_regex(R"(^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$)");
            return std::regex_match(ip, ipv4_regex);
        }

        bool isValidIPv6(const std::string &ip)
        {
            // Simplified IPv6 validation
            std::regex ipv6_regex(R"(^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^::1$|^::$)");
            return std::regex_match(ip, ipv6_regex);
        }

        std::string extractDomainFromEmail(const std::string &email)
        {
            size_t at_pos = email.find('@');
            if (at_pos != std::string::npos && at_pos < email.length() - 1)
            {
                return email.substr(at_pos + 1);
            }
            return "";
        }
    }

    // DNSHealthChecker implementation
    DNSHealthChecker::DNSHealthChecker() : resolver_(nullptr)
    {
        if (!initializeResolver())
        {
            throw std::runtime_error("Failed to initialize DNS resolver");
        }
    }

    DNSHealthChecker::~DNSHealthChecker()
    {
        cleanupResolver();
    }

    bool DNSHealthChecker::initializeResolver()
    {
#ifdef HAVE_LDNS
        ldns_status status = ldns_resolver_new_frm_file(&resolver_, nullptr);
        if (status != LDNS_STATUS_OK)
        {
            last_error_ = "Failed to create resolver: " + std::string(ldns_get_errorstr_by_id(status));
            return false;
        }
        return true;
#else
        last_error_ = "DNS functionality not available - ldns library not found";
        return false;
#endif
    }

    void DNSHealthChecker::cleanupResolver()
    {
#ifdef HAVE_LDNS
        if (resolver_)
        {
            ldns_resolver_deep_free(resolver_);
            resolver_ = nullptr;
        }
#endif
    }

    bool DNSHealthChecker::isValidDomain(const std::string &domain)
    {
        if (domain.empty() || domain.length() > 253)
        {
            return false;
        }

        // Check for invalid characters and patterns
        std::regex domain_regex(R"(^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$)");
        return std::regex_match(domain, domain_regex);
    }

    std::string DNSHealthChecker::getLocalHostname()
    {
        char hostname[256];
        if (gethostname(hostname, sizeof(hostname)) == 0)
        {
            return std::string(hostname);
        }
        return "localhost";
    }

    HealthReport DNSHealthChecker::checkDomain(const std::string &domain)
    {
        HealthReport report(domain);

        if (!isValidDomain(domain))
        {
            CheckInfo invalid_domain("Domain Validation", CheckResult::FAIL, "Invalid domain format");
            invalid_domain.recommendations.push_back("Ensure domain follows proper DNS naming conventions");
            report.checks.push_back(invalid_domain);
            report.overall_healthy = false;
            return report;
        }

        // Perform all checks
        report.checks.push_back(checkSPF(domain));
        report.checks.push_back(checkDKIM(domain));
        report.checks.push_back(checkDMARC(domain));
        report.checks.push_back(checkMX(domain));
        report.checks.push_back(checkPTR(domain));
        report.checks.push_back(checkDNSSEC(domain));
        report.checks.push_back(checkTTL(domain));

        // Determine overall health
        report.overall_healthy = true;
        for (const auto &check : report.checks)
        {
            if (check.result == CheckResult::FAIL)
            {
                report.overall_healthy = false;
                break;
            }
        }

        return report;
    }

    HealthReport DNSHealthChecker::checkLocalMachine()
    {
        std::string hostname = getLocalHostname();
        HealthReport report("Local Machine (" + hostname + ")");

        // Check local DNS configuration
        CheckInfo local_dns("Local DNS Configuration", CheckResult::PASS, "Local DNS resolver accessible");
        if (!resolver_)
        {
            local_dns.result = CheckResult::FAIL;
            local_dns.details = "DNS resolver not available";
            local_dns.recommendations.push_back("Check DNS server configuration");
            local_dns.recommendations.push_back("Verify network connectivity");
        }
        report.checks.push_back(local_dns);

        // Check if hostname resolves
        if (!hostname.empty() && hostname != "localhost")
        {
            auto mx_check = checkMX(hostname);
            mx_check.name = "Local Hostname Resolution";
            report.checks.push_back(mx_check);
        }

        // Determine overall health
        report.overall_healthy = true;
        for (const auto &check : report.checks)
        {
            if (check.result == CheckResult::FAIL)
            {
                report.overall_healthy = false;
                break;
            }
        }

        return report;
    }

    std::vector<std::string> DNSHealthChecker::queryTXT(const std::string &domain)
    {
        std::vector<std::string> results;

#ifdef HAVE_LDNS
        if (!resolver_)
        {
            return results;
        }

        ldns_rdf *domain_rdf = ldns_dname_new_frm_str(domain.c_str());
        if (!domain_rdf)
        {
            return results;
        }

        ldns_pkt *packet = ldns_resolver_query(resolver_, domain_rdf, LDNS_RR_TYPE_TXT, LDNS_RR_CLASS_IN, LDNS_RD);
        ldns_rdf_deep_free(domain_rdf);

        if (!packet)
        {
            return results;
        }

        ldns_rr_list *answer = ldns_pkt_answer(packet);
        if (answer)
        {
            size_t count = ldns_rr_list_rr_count(answer);
            for (size_t i = 0; i < count; i++)
            {
                ldns_rr *rr = ldns_rr_list_rr(answer, i);
                if (ldns_rr_get_type(rr) == LDNS_RR_TYPE_TXT)
                {
                    ldns_rdf *rdf = ldns_rr_rdf(rr, 0);
                    if (rdf)
                    {
                        char *txt_data = ldns_rdf2str(rdf);
                        if (txt_data)
                        {
                            std::string txt_record(txt_data);
                            // Remove quotes if present
                            if (txt_record.front() == '"' && txt_record.back() == '"')
                            {
                                txt_record = txt_record.substr(1, txt_record.length() - 2);
                            }
                            results.push_back(txt_record);
                            free(txt_data);
                        }
                    }
                }
            }
        }

        ldns_pkt_free(packet);
#endif
        return results;
    }

    std::string DNSHealthChecker::getLastError() const
    {
        return last_error_;
    }

    void DNSHealthChecker::setLastError(const std::string &error)
    {
        last_error_ = error;
    }

    std::vector<std::string> DNSHealthChecker::queryMX(const std::string &domain)
    {
        std::vector<std::string> results;

#ifdef HAVE_LDNS
        if (!resolver_)
        {
            return results;
        }

        ldns_rdf *domain_rdf = ldns_dname_new_frm_str(domain.c_str());
        if (!domain_rdf)
        {
            return results;
        }

        ldns_pkt *packet = ldns_resolver_query(resolver_, domain_rdf, LDNS_RR_TYPE_MX, LDNS_RR_CLASS_IN, LDNS_RD);
        ldns_rdf_deep_free(domain_rdf);

        if (!packet)
        {
            return results;
        }

        ldns_rr_list *answer = ldns_pkt_answer(packet);
        if (answer)
        {
            size_t count = ldns_rr_list_rr_count(answer);
            for (size_t i = 0; i < count; i++)
            {
                ldns_rr *rr = ldns_rr_list_rr(answer, i);
                if (ldns_rr_get_type(rr) == LDNS_RR_TYPE_MX)
                {
                    ldns_rdf *priority_rdf = ldns_rr_rdf(rr, 0);
                    ldns_rdf *exchange_rdf = ldns_rr_rdf(rr, 1);

                    if (priority_rdf && exchange_rdf)
                    {
                        char *priority_str = ldns_rdf2str(priority_rdf);
                        char *exchange_str = ldns_rdf2str(exchange_rdf);

                        if (priority_str && exchange_str)
                        {
                            std::string mx_record = std::string(priority_str) + " " + std::string(exchange_str);
                            results.push_back(mx_record);
                            free(priority_str);
                            free(exchange_str);
                        }
                    }
                }
            }
        }

        ldns_pkt_free(packet);
#endif
        return results;
    }

    bool DNSHealthChecker::queryDNSSEC(const std::string &domain)
    {
#ifdef HAVE_LDNS
        if (!resolver_)
        {
            return false;
        }

        ldns_rdf *domain_rdf = ldns_dname_new_frm_str(domain.c_str());
        if (!domain_rdf)
        {
            return false;
        }

        // Query for DNSKEY records
        ldns_pkt *packet = ldns_resolver_query(resolver_, domain_rdf, LDNS_RR_TYPE_DNSKEY, LDNS_RR_CLASS_IN, LDNS_RD);
        ldns_rdf_deep_free(domain_rdf);

        if (!packet)
        {
            return false;
        }

        ldns_rr_list *answer = ldns_pkt_answer(packet);
        bool has_dnssec = answer && ldns_rr_list_rr_count(answer) > 0;

        ldns_pkt_free(packet);
        return has_dnssec;
#else
        return false;
#endif
    }

    uint32_t DNSHealthChecker::queryTTL(const std::string &domain, const std::string &record_type)
    {
#ifdef HAVE_LDNS
        if (!resolver_)
        {
            return 0;
        }

        ldns_rr_type type = LDNS_RR_TYPE_A;
        if (record_type == "MX")
        {
            type = LDNS_RR_TYPE_MX;
        }
        else if (record_type == "TXT")
        {
            type = LDNS_RR_TYPE_TXT;
        }

        ldns_rdf *domain_rdf = ldns_dname_new_frm_str(domain.c_str());
        if (!domain_rdf)
        {
            return 0;
        }

        ldns_pkt *packet = ldns_resolver_query(resolver_, domain_rdf, type, LDNS_RR_CLASS_IN, LDNS_RD);
        ldns_rdf_deep_free(domain_rdf);

        if (!packet)
        {
            return 0;
        }

        uint32_t ttl = 0;
        ldns_rr_list *answer = ldns_pkt_answer(packet);
        if (answer && ldns_rr_list_rr_count(answer) > 0)
        {
            ldns_rr *rr = ldns_rr_list_rr(answer, 0);
            ttl = ldns_rr_ttl(rr);
        }

        ldns_pkt_free(packet);
        return ttl;
#else
        return 0;
#endif
    }

    bool DNSHealthChecker::validateMXPriorities(const std::vector<std::string> &mx_records)
    {
        if (mx_records.size() <= 1)
        {
            return true; // Single MX record is fine
        }

        std::vector<int> priorities;
        for (const auto &record : mx_records)
        {
            std::istringstream iss(record);
            int priority;
            if (iss >> priority)
            {
                priorities.push_back(priority);
            }
        }

        // Check for duplicate priorities
        std::sort(priorities.begin(), priorities.end());
        for (size_t i = 1; i < priorities.size(); i++)
        {
            if (priorities[i] == priorities[i - 1])
            {
                return false; // Duplicate priority found
            }
        }

        return true;
    }

} // namespace dns_health_checker
