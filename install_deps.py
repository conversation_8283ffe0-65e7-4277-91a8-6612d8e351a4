#!/usr/bin/env python3
"""
Dependency installer for Cybersecurity Scanner
Downloads and installs all required tools and libraries
"""

import subprocess
import sys
import os
import platform
import urllib.request
import zipfile
import tarfile
import shutil
from pathlib import Path

def check_internet_connection():
    """Check if internet connection is available"""
    try:
        urllib.request.urlopen('https://www.google.com', timeout=5)
        return True
    except:
        return False

def download_file(url, filename):
    """Download a file from URL"""
    try:
        print(f"Downloading {filename}...")
        urllib.request.urlretrieve(url, filename)
        return True
    except Exception as e:
        print(f"Failed to download {filename}: {e}")
        return False

def extract_archive(filename, extract_to):
    """Extract zip or tar archive"""
    try:
        if filename.endswith('.zip'):
            with zipfile.ZipFile(filename, 'r') as zip_ref:
                zip_ref.extractall(extract_to)
        elif filename.endswith(('.tar.gz', '.tgz')):
            with tarfile.open(filename, 'r:gz') as tar_ref:
                tar_ref.extractall(extract_to)
        elif filename.endswith('.tar'):
            with tarfile.open(filename, 'r') as tar_ref:
                tar_ref.extractall(extract_to)
        return True
    except Exception as e:
        print(f"Failed to extract {filename}: {e}")
        return False

def install_portable_nmap():
    """Install portable nmap for Windows"""
    if platform.system().lower() != "windows":
        return True
    
    print("Installing portable nmap for Windows...")
    
    # Create tools directory
    os.makedirs("tools", exist_ok=True)
    
    # Download nmap
    nmap_url = "https://nmap.org/dist/nmap-7.94-win32.zip"
    nmap_file = "tools/nmap.zip"
    
    if download_file(nmap_url, nmap_file):
        if extract_archive(nmap_file, "tools"):
            print("✓ Portable nmap installed successfully")
            os.remove(nmap_file)
            return True
    
    print("✗ Failed to install portable nmap")
    return False

def install_masscan():
    """Install masscan port scanner"""
    print("Installing masscan...")
    
    system = platform.system().lower()
    
    if system == "linux":
        # Try to install via package manager first
        managers = [
            ("apt-get", "sudo apt-get install -y masscan"),
            ("yum", "sudo yum install -y masscan"),
            ("dnf", "sudo dnf install -y masscan"),
        ]
        
        for manager, cmd in managers:
            if shutil.which(manager.split()[0] if manager != "apt-get" else "apt"):
                result = subprocess.run(cmd, shell=True, capture_output=True)
                if result.returncode == 0:
                    print("✓ masscan installed via package manager")
                    return True
        
        # Build from source if package manager fails
        print("Building masscan from source...")
        os.makedirs("tools", exist_ok=True)
        
        # Clone and build masscan
        commands = [
            "cd tools && git clone https://github.com/robertdavidgraham/masscan",
            "cd tools/masscan && make",
            "cd tools/masscan && sudo make install"
        ]
        
        for cmd in commands:
            result = subprocess.run(cmd, shell=True)
            if result.returncode != 0:
                print("✗ Failed to build masscan from source")
                return False
        
        print("✓ masscan built and installed successfully")
        return True
    
    elif system == "darwin":  # macOS
        if shutil.which("brew"):
            result = subprocess.run("brew install masscan", shell=True)
            if result.returncode == 0:
                print("✓ masscan installed via Homebrew")
                return True
    
    elif system == "windows":
        print("masscan is not available for Windows, using nmap instead")
        return True
    
    print("✗ Could not install masscan")
    return False

def download_geoip_database():
    """Download GeoIP database"""
    print("Downloading GeoIP database...")
    
    os.makedirs("data", exist_ok=True)
    
    # Download GeoLite2 City database (free version)
    geoip_url = "https://github.com/P3TERX/GeoLite.mmdb/raw/download/GeoLite2-City.mmdb"
    geoip_file = "data/GeoLite2-City.mmdb"
    
    if download_file(geoip_url, geoip_file):
        print("✓ GeoIP database downloaded successfully")
        return True
    else:
        print("✗ Failed to download GeoIP database")
        return False

def install_additional_tools():
    """Install additional security tools"""
    print("Installing additional security tools...")
    
    system = platform.system().lower()
    
    tools = []
    
    if system == "linux":
        tools = [
            ("dig", "dnsutils"),
            ("whois", "whois"),
            ("curl", "curl"),
            ("openssl", "openssl")
        ]
        
        # Try different package managers
        managers = [
            ("apt-get", "sudo apt-get install -y"),
            ("yum", "sudo yum install -y"),
            ("dnf", "sudo dnf install -y"),
        ]
        
        for manager, install_cmd in managers:
            if shutil.which(manager.split()[0] if manager != "apt-get" else "apt"):
                for tool, package in tools:
                    if not shutil.which(tool):
                        print(f"Installing {tool}...")
                        result = subprocess.run(f"{install_cmd} {package}", shell=True)
                        if result.returncode == 0:
                            print(f"✓ {tool} installed successfully")
                        else:
                            print(f"✗ Failed to install {tool}")
                break
    
    elif system == "darwin":  # macOS
        if shutil.which("brew"):
            tools = ["nmap", "whois", "curl", "openssl"]
            for tool in tools:
                if not shutil.which(tool):
                    print(f"Installing {tool}...")
                    result = subprocess.run(f"brew install {tool}", shell=True)
                    if result.returncode == 0:
                        print(f"✓ {tool} installed successfully")
    
    return True

def create_config_file():
    """Create configuration file"""
    print("Creating configuration file...")
    
    config = {
        "tools": {
            "nmap_path": "nmap" if shutil.which("nmap") else "tools/nmap/nmap.exe",
            "masscan_path": "masscan" if shutil.which("masscan") else None,
            "geoip_db": "data/GeoLite2-City.mmdb"
        },
        "scanning": {
            "default_ports": "1-1000",
            "timeout": 30,
            "max_threads": 50
        },
        "output": {
            "format": "json",
            "save_results": True,
            "results_dir": "results"
        }
    }
    
    import json
    with open("config.json", "w") as f:
        json.dump(config, f, indent=2)
    
    print("✓ Configuration file created")
    return True

def main():
    """Main installation function"""
    print("=== Cybersecurity Scanner Dependency Installer ===")
    print(f"Platform: {platform.system()} {platform.release()}")
    print()
    
    if not check_internet_connection():
        print("✗ No internet connection available")
        print("Please connect to the internet and try again")
        return 1
    
    success = True
    
    # Install portable nmap for Windows
    if not install_portable_nmap():
        success = False
    
    # Install masscan
    if not install_masscan():
        print("Warning: masscan installation failed")
    
    # Download GeoIP database
    if not download_geoip_database():
        success = False
    
    # Install additional tools
    if not install_additional_tools():
        print("Warning: some additional tools may not be available")
    
    # Create config file
    if not create_config_file():
        success = False
    
    print()
    if success:
        print("✓ Dependency installation completed successfully!")
    else:
        print("✗ Dependency installation completed with errors")
        print("Some features may not work properly")
    
    print("\nNext steps:")
    print("1. Run: python setup.py")
    print("2. Build the project using the generated build script")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
