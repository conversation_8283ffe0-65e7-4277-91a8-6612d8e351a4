// Cybersecurity Scanner JavaScript

let currentScanResults = null;
let scanInProgress = false;

// Initialize the application
document.addEventListener("DOMContentLoaded", function () {
  // Set up event listeners
  document
    .getElementById("targetInput")
    .addEventListener("keypress", function (e) {
      if (e.key === "Enter") {
        startScan();
      }
    });

  // Set up scan type change handler
  document.getElementById("scanType").addEventListener("change", function () {
    updateScanOptions();
  });

  updateScanOptions();
});

// Update scan options based on scan type
function updateScanOptions() {
  const scanType = document.getElementById("scanType").value;
  const checkboxes = {
    dnsCheck: document.getElementById("dnsCheck"),
    portScan: document.getElementById("portScan"),
    sslCheck: document.getElementById("sslCheck"),
    httpSecurity: document.getElementById("httpSecurity"),
    emailSecurity: document.getElementById("emailSecurity"),
    geoipLookup: document.getElementById("geoipLookup"),
  };

  switch (scanType) {
    case "quick":
      checkboxes.dnsCheck.checked = true;
      checkboxes.portScan.checked = true;
      checkboxes.sslCheck.checked = true;
      checkboxes.httpSecurity.checked = false;
      checkboxes.emailSecurity.checked = false;
      checkboxes.geoipLookup.checked = true;
      break;
    case "comprehensive":
      Object.values(checkboxes).forEach((cb) => (cb.checked = true));
      break;
    case "deep":
      Object.values(checkboxes).forEach((cb) => (cb.checked = true));
      break;
  }
}

// Start the security scan
async function startScan() {
  if (scanInProgress) {
    return;
  }

  const target = document.getElementById("targetInput").value.trim();
  if (!target) {
    showError("Please enter a domain or IP address");
    return;
  }

  if (!isValidTarget(target)) {
    showError("Please enter a valid domain or IP address");
    return;
  }

  scanInProgress = true;
  showLoadingSection();

  try {
    const scanOptions = getScanOptions();
    const scanType = document.getElementById("scanType").value;

    // Simulate scan progress
    simulateScanProgress();

    // Call the Python orchestrator
    const results = await callScanAPI(target, scanType, scanOptions);

    if (results.error) {
      throw new Error(results.error);
    }

    currentScanResults = results;
    displayResults(results);
  } catch (error) {
    console.error("Scan failed:", error);
    showError("Scan failed: " + error.message);
    hideLoadingSection();
  } finally {
    scanInProgress = false;
  }
}

// Validate target input
function isValidTarget(target) {
  // Check if it's a valid domain or IP address
  const domainRegex =
    /^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$/;
  const ipRegex =
    /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;

  return (
    domainRegex.test(target) || ipRegex.test(target) || target === "localhost"
  );
}

// Get scan options from checkboxes
function getScanOptions() {
  return {
    dns_check: document.getElementById("dnsCheck").checked,
    port_scan: document.getElementById("portScan").checked,
    ssl_check: document.getElementById("sslCheck").checked,
    http_security: document.getElementById("httpSecurity").checked,
    email_security: document.getElementById("emailSecurity").checked,
    geoip_lookup: document.getElementById("geoipLookup").checked,
  };
}

// Call the scan API (Python orchestrator)
async function callScanAPI(target, scanType, scanOptions) {
  try {
    // Start the scan
    const startResponse = await fetch("/api/scan", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        target: target,
        scan_type: scanType,
        scan_options: scanOptions,
      }),
    });

    if (!startResponse.ok) {
      throw new Error(
        `HTTP ${startResponse.status}: ${startResponse.statusText}`
      );
    }

    const startData = await startResponse.json();

    if (startData.error) {
      throw new Error(startData.error);
    }

    const scanId = startData.scan_id;

    // Poll for results
    return await pollScanResults(scanId);
  } catch (error) {
    console.error("API call failed:", error);
    throw error;
  }
}

// Poll for scan results
async function pollScanResults(scanId) {
  const maxAttempts = 120; // 2 minutes max
  let attempts = 0;

  while (attempts < maxAttempts) {
    try {
      // Check scan status
      const statusResponse = await fetch(`/api/status?scan_id=${scanId}`);

      if (!statusResponse.ok) {
        throw new Error(`Status check failed: ${statusResponse.status}`);
      }

      const statusData = await statusResponse.json();

      if (statusData.status === "completed") {
        // Get results
        const resultsResponse = await fetch(`/api/results?scan_id=${scanId}`);

        if (!resultsResponse.ok) {
          throw new Error(`Results fetch failed: ${resultsResponse.status}`);
        }

        return await resultsResponse.json();
      } else if (statusData.status === "failed") {
        throw new Error(statusData.error || "Scan failed");
      }

      // Update progress if available
      if (statusData.progress !== undefined) {
        updateScanProgress(statusData.progress, statusData.current_step);
      }

      // Wait before next poll
      await new Promise((resolve) => setTimeout(resolve, 1000));
      attempts++;
    } catch (error) {
      console.error("Polling error:", error);
      throw error;
    }
  }

  throw new Error("Scan timeout - results not available");
}

// Update scan progress
function updateScanProgress(progress, currentStep) {
  const progressFill = document.getElementById("progressFill");
  const scanProgress = document.getElementById("scanProgress");

  if (progressFill) {
    progressFill.style.width = progress + "%";
  }

  if (scanProgress && currentStep) {
    scanProgress.textContent = currentStep;
  }
}

// Generate mock results for demonstration
function generateMockResults(target, scanType, scanOptions) {
  const timestamp = new Date().toISOString();
  const duration = Math.random() * 30 + 10; // 10-40 seconds

  return {
    scan_id: `scan_${Date.now()}`,
    target: target,
    timestamp: timestamp,
    scan_options: scanOptions,
    duration: duration,
    results: {
      dns_health: scanOptions.dns_check
        ? {
            domain: target,
            overall_healthy: Math.random() > 0.3,
            checks: [
              {
                name: "A Record",
                status: "PASS",
                details: "IPv4 address resolved successfully",
              },
              {
                name: "MX Record",
                status: Math.random() > 0.2 ? "PASS" : "FAIL",
                details: "Mail exchange records found",
              },
              {
                name: "NS Record",
                status: "PASS",
                details: "Name servers configured",
              },
            ],
          }
        : null,
      port_scan: scanOptions.port_scan
        ? {
            target: target,
            open_ports: generateMockPorts(),
            scan_duration: Math.random() * 20 + 5,
          }
        : null,
      ssl_tls: scanOptions.ssl_check
        ? {
            domain: target,
            ssl_enabled: true,
            certificate_valid: Math.random() > 0.2,
            security_issues:
              Math.random() > 0.5 ? [] : ["Certificate expires in 15 days"],
            certificate_info: {
              subject: { CN: target },
              issuer: { CN: "Let's Encrypt Authority X3" },
              not_after: new Date(
                Date.now() + 30 * 24 * 60 * 60 * 1000
              ).toISOString(),
              days_until_expiry: 30,
            },
          }
        : null,
      geoip: scanOptions.geoip_lookup
        ? {
            ip_address: "*******",
            geolocation: {
              country: "United States",
              city: "Mountain View",
              latitude: 37.4056,
              longitude: -122.0775,
            },
            asn_info: {
              asn: "AS15169",
              organization: "Google LLC",
            },
          }
        : null,
    },
    summary: {
      total_checks: Object.values(scanOptions).filter(Boolean).length,
      successful_checks: Math.floor(Math.random() * 6) + 3,
      failed_checks: Math.floor(Math.random() * 2),
      security_issues: Math.floor(Math.random() * 5),
      risk_score: Math.floor(Math.random() * 100),
      open_ports: generateMockPorts().length,
    },
  };
}

// Generate mock port scan results
function generateMockPorts() {
  const commonPorts = [22, 80, 443, 25, 53, 110, 143, 993, 995];
  const openPorts = [];

  commonPorts.forEach((port) => {
    if (Math.random() > 0.7) {
      openPorts.push({
        port: port,
        protocol: "tcp",
        state: "open",
        service: getServiceName(port),
      });
    }
  });

  return openPorts;
}

// Get service name for port
function getServiceName(port) {
  const services = {
    22: "ssh",
    80: "http",
    443: "https",
    25: "smtp",
    53: "dns",
    110: "pop3",
    143: "imap",
    993: "imaps",
    995: "pop3s",
  };
  return services[port] || "unknown";
}

// Show loading section
function showLoadingSection() {
  document.getElementById("loadingSection").style.display = "block";
  document.getElementById("resultsSection").style.display = "none";
  document.getElementById("scanButton").disabled = true;
  document.getElementById("scanButton").innerHTML =
    '<i class="fas fa-spinner fa-spin"></i> Scanning...';
}

// Hide loading section
function hideLoadingSection() {
  document.getElementById("loadingSection").style.display = "none";
  document.getElementById("scanButton").disabled = false;
  document.getElementById("scanButton").innerHTML =
    '<i class="fas fa-search"></i> Start Scan';
}

// Simulate scan progress
function simulateScanProgress() {
  const progressFill = document.getElementById("progressFill");
  const scanProgress = document.getElementById("scanProgress");

  const steps = [
    "Initializing scan...",
    "Checking DNS health...",
    "Scanning ports...",
    "Analyzing SSL/TLS...",
    "Checking HTTP security...",
    "Verifying email security...",
    "Performing GeoIP lookup...",
    "Generating report...",
  ];

  let currentStep = 0;
  const interval = setInterval(() => {
    if (currentStep < steps.length) {
      const progress = ((currentStep + 1) / steps.length) * 100;
      progressFill.style.width = progress + "%";
      scanProgress.textContent = steps[currentStep];
      currentStep++;
    } else {
      clearInterval(interval);
    }
  }, 600);
}

// Display scan results
function displayResults(results) {
  hideLoadingSection();
  document.getElementById("resultsSection").style.display = "block";

  // Update summary cards
  updateSummaryCards(results.summary);

  // Update target info
  updateTargetInfo(results);

  // Update individual result tabs
  updateDNSResults(results.results.dns_health);
  updatePortResults(results.results.port_scan);
  updateSSLResults(results.results.ssl_tls);
  updateGeoIPResults(results.results.geoip);

  // Show overview tab by default
  showTab("overview");
}

// Update summary cards
function updateSummaryCards(summary) {
  document.getElementById("riskScoreValue").textContent = summary.risk_score;
  document.getElementById("openPortsCount").textContent = summary.open_ports;
  document.getElementById("securityIssuesCount").textContent =
    summary.security_issues;
  document.getElementById("scanDuration").textContent =
    Math.round(currentScanResults.duration) + "s";

  // Update risk score color
  const riskCircle = document.getElementById("riskScoreCircle");
  riskCircle.className = "score-circle";
  if (summary.risk_score < 30) {
    riskCircle.classList.add("low");
  } else if (summary.risk_score < 70) {
    riskCircle.classList.add("medium");
  } else {
    riskCircle.classList.add("high");
  }
}

// Update target information
function updateTargetInfo(results) {
  const targetInfo = document.getElementById("targetInfo");
  targetInfo.innerHTML = `
        <div class="info-item">
            <strong>Target:</strong> ${results.target}
        </div>
        <div class="info-item">
            <strong>Scan ID:</strong> ${results.scan_id}
        </div>
        <div class="info-item">
            <strong>Timestamp:</strong> ${new Date(
              results.timestamp
            ).toLocaleString()}
        </div>
        <div class="info-item">
            <strong>Duration:</strong> ${Math.round(results.duration)} seconds
        </div>
    `;
}

// Update DNS results
function updateDNSResults(dnsResults) {
  const dnsResultsDiv = document.getElementById("dnsResults");

  if (!dnsResults) {
    dnsResultsDiv.innerHTML = "<p>DNS check was not performed.</p>";
    return;
  }

  let html = `<div class="dns-status ${
    dnsResults.overall_healthy ? "healthy" : "unhealthy"
  }">
        Overall DNS Health: ${
          dnsResults.overall_healthy ? "Healthy" : "Issues Found"
        }
    </div>`;

  html += '<div class="dns-checks">';
  dnsResults.checks.forEach((check) => {
    html += `
            <div class="check-item ${check.status.toLowerCase()}">
                <div class="check-name">${check.name}</div>
                <div class="check-status">${check.status}</div>
                <div class="check-details">${check.details}</div>
            </div>
        `;
  });
  html += "</div>";

  dnsResultsDiv.innerHTML = html;
}

// Update port scan results
function updatePortResults(portResults) {
  const portResultsDiv = document.getElementById("portResults");

  if (!portResults) {
    portResultsDiv.innerHTML = "<p>Port scan was not performed.</p>";
    return;
  }

  let html = `<div class="port-summary">
        Found ${portResults.open_ports.length} open ports
    </div>`;

  if (portResults.open_ports.length > 0) {
    html += '<div class="port-list">';
    portResults.open_ports.forEach((port) => {
      html += `
                <div class="port-item">
                    <div class="port-number">${port.port}/${port.protocol}</div>
                    <div class="port-service">${port.service}</div>
                    <div class="port-state">${port.state}</div>
                </div>
            `;
    });
    html += "</div>";
  }

  portResultsDiv.innerHTML = html;
}

// Update SSL results
function updateSSLResults(sslResults) {
  const sslResultsDiv = document.getElementById("sslResults");

  if (!sslResults) {
    sslResultsDiv.innerHTML = "<p>SSL check was not performed.</p>";
    return;
  }

  let html = `<div class="ssl-status ${
    sslResults.certificate_valid ? "valid" : "invalid"
  }">
        SSL Certificate: ${sslResults.certificate_valid ? "Valid" : "Invalid"}
    </div>`;

  if (sslResults.certificate_info) {
    html += `
            <div class="ssl-info">
                <div class="info-item">
                    <strong>Subject:</strong> ${
                      sslResults.certificate_info.subject.CN
                    }
                </div>
                <div class="info-item">
                    <strong>Issuer:</strong> ${
                      sslResults.certificate_info.issuer.CN
                    }
                </div>
                <div class="info-item">
                    <strong>Expires:</strong> ${new Date(
                      sslResults.certificate_info.not_after
                    ).toLocaleDateString()}
                </div>
                <div class="info-item">
                    <strong>Days until expiry:</strong> ${
                      sslResults.certificate_info.days_until_expiry
                    }
                </div>
            </div>
        `;
  }

  if (sslResults.security_issues && sslResults.security_issues.length > 0) {
    html += '<div class="ssl-issues"><h4>Security Issues:</h4><ul>';
    sslResults.security_issues.forEach((issue) => {
      html += `<li>${issue}</li>`;
    });
    html += "</ul></div>";
  }

  sslResultsDiv.innerHTML = html;
}

// Update GeoIP results
function updateGeoIPResults(geoipResults) {
  const geoipResultsDiv = document.getElementById("geoipResults");

  if (!geoipResults) {
    geoipResultsDiv.innerHTML = "<p>GeoIP lookup was not performed.</p>";
    return;
  }

  let html = "";

  if (geoipResults.geolocation) {
    html += `
            <div class="geoip-section">
                <h4>Geographic Location</h4>
                <div class="info-item">
                    <strong>Country:</strong> ${geoipResults.geolocation.country}
                </div>
                <div class="info-item">
                    <strong>City:</strong> ${geoipResults.geolocation.city}
                </div>
                <div class="info-item">
                    <strong>Coordinates:</strong> ${geoipResults.geolocation.latitude}, ${geoipResults.geolocation.longitude}
                </div>
            </div>
        `;
  }

  if (geoipResults.asn_info) {
    html += `
            <div class="geoip-section">
                <h4>Network Information</h4>
                <div class="info-item">
                    <strong>ASN:</strong> ${geoipResults.asn_info.asn}
                </div>
                <div class="info-item">
                    <strong>Organization:</strong> ${geoipResults.asn_info.organization}
                </div>
            </div>
        `;
  }

  geoipResultsDiv.innerHTML = html;
}

// Show specific tab
function showTab(tabName) {
  // Hide all tab contents
  document.querySelectorAll(".tab-content").forEach((tab) => {
    tab.classList.remove("active");
  });

  // Remove active class from all tab buttons
  document.querySelectorAll(".tab-button").forEach((button) => {
    button.classList.remove("active");
  });

  // Show selected tab content
  document.getElementById(tabName + "Tab").classList.add("active");

  // Add active class to selected tab button
  event.target.classList.add("active");
}

// Export results
function exportResults(format) {
  if (!currentScanResults) {
    showError("No scan results to export");
    return;
  }

  if (format === "json") {
    const dataStr = JSON.stringify(currentScanResults, null, 2);
    const dataBlob = new Blob([dataStr], { type: "application/json" });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `scan_${currentScanResults.target}_${Date.now()}.json`;
    link.click();
    URL.revokeObjectURL(url);
  } else if (format === "pdf") {
    // PDF export would require additional library
    showError("PDF export not yet implemented");
  }
}

// Clear results
function clearResults() {
  document.getElementById("resultsSection").style.display = "none";
  currentScanResults = null;
}

// Show error modal
function showError(message) {
  document.getElementById("errorMessage").textContent = message;
  document.getElementById("errorModal").style.display = "block";
}

// Open modal
function openModal(modalId) {
  document.getElementById(modalId).style.display = "block";
}

// Close modal
function closeModal(modalId) {
  document.getElementById(modalId).style.display = "none";
}

// Close modal when clicking outside
window.onclick = function (event) {
  if (event.target.classList.contains("modal")) {
    event.target.style.display = "none";
  }
};
