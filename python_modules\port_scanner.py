#!/usr/bin/env python3
"""
Port Scanner Module
Performs network port scanning using nmap and custom implementations
"""

import subprocess
import socket
import threading
import json
import datetime
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import xml.etree.ElementTree as ET

class PortScanner:
    def __init__(self):
        self.timeout = 3
        self.max_threads = 50
        self.nmap_available = self._check_nmap_availability()
    
    def _check_nmap_availability(self):
        """Check if nmap is available"""
        try:
            subprocess.run(['nmap', '--version'], capture_output=True, check=True)
            return True
        except:
            return False
    
    def scan_ports_nmap(self, target, ports="1-1000", scan_type="syn"):
        """Scan ports using nmap"""
        if not self.nmap_available:
            return {"error": "nmap not available"}
        
        result = {
            "target": target,
            "scan_type": scan_type,
            "timestamp": datetime.datetime.now().isoformat(),
            "ports": [],
            "open_ports": [],
            "filtered_ports": [],
            "closed_ports": [],
            "scan_duration": 0
        }
        
        try:
            start_time = time.time()
            
            # Build nmap command
            cmd = ['nmap', '-p', ports, target]
            
            if scan_type == "syn":
                cmd.append('-sS')
            elif scan_type == "tcp":
                cmd.append('-sT')
            elif scan_type == "udp":
                cmd.append('-sU')
            
            cmd.extend(['-oX', '-'])  # XML output to stdout
            
            # Run nmap
            process = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if process.returncode == 0:
                result["scan_duration"] = time.time() - start_time
                result.update(self._parse_nmap_xml(process.stdout))
            else:
                result["error"] = process.stderr
        
        except subprocess.TimeoutExpired:
            result["error"] = "Scan timeout"
        except Exception as e:
            result["error"] = str(e)
        
        return result
    
    def _parse_nmap_xml(self, xml_output):
        """Parse nmap XML output"""
        result = {
            "ports": [],
            "open_ports": [],
            "filtered_ports": [],
            "closed_ports": [],
            "host_info": {}
        }
        
        try:
            root = ET.fromstring(xml_output)
            
            # Get host information
            host = root.find('host')
            if host is not None:
                # Get host status
                status = host.find('status')
                if status is not None:
                    result["host_info"]["status"] = status.get('state')
                
                # Get addresses
                addresses = host.findall('address')
                for addr in addresses:
                    addr_type = addr.get('addrtype')
                    addr_value = addr.get('addr')
                    result["host_info"][f"{addr_type}_address"] = addr_value
                
                # Get ports
                ports = host.find('ports')
                if ports is not None:
                    for port in ports.findall('port'):
                        port_id = port.get('portid')
                        protocol = port.get('protocol')
                        
                        state = port.find('state')
                        if state is not None:
                            port_state = state.get('state')
                            
                            service = port.find('service')
                            service_name = service.get('name') if service is not None else 'unknown'
                            service_product = service.get('product') if service is not None else ''
                            service_version = service.get('version') if service is not None else ''
                            
                            port_info = {
                                "port": int(port_id),
                                "protocol": protocol,
                                "state": port_state,
                                "service": service_name,
                                "product": service_product,
                                "version": service_version
                            }
                            
                            result["ports"].append(port_info)
                            
                            if port_state == 'open':
                                result["open_ports"].append(port_info)
                            elif port_state == 'filtered':
                                result["filtered_ports"].append(port_info)
                            elif port_state == 'closed':
                                result["closed_ports"].append(port_info)
        
        except Exception as e:
            result["parse_error"] = str(e)
        
        return result
    
    def scan_ports_custom(self, target, ports, scan_type="tcp"):
        """Custom port scanner implementation"""
        result = {
            "target": target,
            "scan_type": scan_type,
            "timestamp": datetime.datetime.now().isoformat(),
            "ports": [],
            "open_ports": [],
            "closed_ports": [],
            "scan_duration": 0
        }
        
        start_time = time.time()
        
        # Parse port range
        port_list = self._parse_port_range(ports)
        
        # Scan ports using threading
        with ThreadPoolExecutor(max_workers=self.max_threads) as executor:
            if scan_type == "tcp":
                futures = {executor.submit(self._scan_tcp_port, target, port): port for port in port_list}
            elif scan_type == "udp":
                futures = {executor.submit(self._scan_udp_port, target, port): port for port in port_list}
            else:
                result["error"] = f"Unsupported scan type: {scan_type}"
                return result
            
            for future in as_completed(futures):
                port = futures[future]
                try:
                    port_result = future.result()
                    result["ports"].append(port_result)
                    
                    if port_result["state"] == "open":
                        result["open_ports"].append(port_result)
                    else:
                        result["closed_ports"].append(port_result)
                
                except Exception as e:
                    result["ports"].append({
                        "port": port,
                        "state": "error",
                        "error": str(e)
                    })
        
        result["scan_duration"] = time.time() - start_time
        return result
    
    def _parse_port_range(self, ports):
        """Parse port range string into list of ports"""
        port_list = []
        
        if isinstance(ports, str):
            parts = ports.split(',')
            for part in parts:
                part = part.strip()
                if '-' in part:
                    start, end = map(int, part.split('-'))
                    port_list.extend(range(start, end + 1))
                else:
                    port_list.append(int(part))
        elif isinstance(ports, list):
            port_list = ports
        else:
            port_list = [ports]
        
        return port_list
    
    def _scan_tcp_port(self, target, port):
        """Scan a single TCP port"""
        result = {
            "port": port,
            "protocol": "tcp",
            "state": "closed",
            "service": None,
            "banner": None
        }
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            
            connection_result = sock.connect_ex((target, port))
            
            if connection_result == 0:
                result["state"] = "open"
                result["service"] = self._identify_service(port)
                
                # Try to grab banner
                try:
                    sock.send(b'HEAD / HTTP/1.0\r\n\r\n')
                    banner = sock.recv(1024).decode('utf-8', errors='ignore').strip()
                    if banner:
                        result["banner"] = banner[:200]  # Limit banner length
                except:
                    pass
            
            sock.close()
        
        except Exception as e:
            result["error"] = str(e)
        
        return result
    
    def _scan_udp_port(self, target, port):
        """Scan a single UDP port"""
        result = {
            "port": port,
            "protocol": "udp",
            "state": "closed",
            "service": None
        }
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(self.timeout)
            
            # Send UDP packet
            sock.sendto(b'test', (target, port))
            
            try:
                data, addr = sock.recvfrom(1024)
                result["state"] = "open"
                result["service"] = self._identify_service(port, "udp")
            except socket.timeout:
                # No response could mean open or filtered
                result["state"] = "open|filtered"
            
            sock.close()
        
        except Exception as e:
            result["error"] = str(e)
        
        return result
    
    def _identify_service(self, port, protocol="tcp"):
        """Identify common services by port number"""
        common_services = {
            "tcp": {
                21: "ftp", 22: "ssh", 23: "telnet", 25: "smtp", 53: "dns",
                80: "http", 110: "pop3", 143: "imap", 443: "https", 993: "imaps",
                995: "pop3s", 587: "smtp", 465: "smtps", 3389: "rdp", 5432: "postgresql",
                3306: "mysql", 1433: "mssql", 6379: "redis", 27017: "mongodb"
            },
            "udp": {
                53: "dns", 67: "dhcp", 68: "dhcp", 69: "tftp", 123: "ntp",
                161: "snmp", 162: "snmp-trap", 514: "syslog"
            }
        }
        
        return common_services.get(protocol, {}).get(port, "unknown")
    
    def scan_top_ports(self, target, count=1000):
        """Scan top N most common ports"""
        if self.nmap_available:
            return self.scan_ports_nmap(target, f"--top-ports {count}")
        else:
            # Fallback to common ports
            top_ports = [
                21, 22, 23, 25, 53, 80, 110, 111, 135, 139, 143, 443, 993, 995,
                587, 465, 389, 636, 88, 464, 445, 139, 3389, 5985, 5986, 1433,
                3306, 5432, 6379, 27017, 1521, 1830, 2049, 3260, 5060, 5061
            ]
            
            # Extend to requested count
            if count > len(top_ports):
                top_ports.extend(range(1, min(count - len(top_ports) + 1, 65536)))
            
            return self.scan_ports_custom(target, top_ports[:count])
    
    def detect_os_fingerprint(self, target):
        """Attempt OS fingerprinting"""
        if not self.nmap_available:
            return {"error": "OS fingerprinting requires nmap"}
        
        result = {
            "target": target,
            "timestamp": datetime.datetime.now().isoformat(),
            "os_matches": [],
            "device_type": None,
            "accuracy": 0
        }
        
        try:
            cmd = ['nmap', '-O', target, '-oX', '-']
            process = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
            
            if process.returncode == 0:
                result.update(self._parse_os_fingerprint(process.stdout))
            else:
                result["error"] = process.stderr
        
        except Exception as e:
            result["error"] = str(e)
        
        return result
    
    def _parse_os_fingerprint(self, xml_output):
        """Parse OS fingerprinting results from nmap XML"""
        result = {
            "os_matches": [],
            "device_type": None,
            "accuracy": 0
        }
        
        try:
            root = ET.fromstring(xml_output)
            host = root.find('host')
            
            if host is not None:
                os_elem = host.find('os')
                if os_elem is not None:
                    # Get OS matches
                    for osmatch in os_elem.findall('osmatch'):
                        match_info = {
                            "name": osmatch.get('name'),
                            "accuracy": int(osmatch.get('accuracy', 0)),
                            "line": osmatch.get('line')
                        }
                        result["os_matches"].append(match_info)
                    
                    # Get best match
                    if result["os_matches"]:
                        best_match = max(result["os_matches"], key=lambda x: x["accuracy"])
                        result["accuracy"] = best_match["accuracy"]
                        result["device_type"] = best_match["name"]
        
        except Exception as e:
            result["parse_error"] = str(e)
        
        return result

def main():
    """Test the port scanner"""
    scanner = PortScanner()
    
    target = "scanme.nmap.org"
    
    print(f"Testing port scan for {target}...")
    
    # Test top ports scan
    if scanner.nmap_available:
        print("Using nmap...")
        result = scanner.scan_top_ports(target, 100)
    else:
        print("Using custom scanner...")
        result = scanner.scan_ports_custom(target, "80,443,22,21,25")
    
    print(json.dumps(result, indent=2))

if __name__ == "__main__":
    main()
